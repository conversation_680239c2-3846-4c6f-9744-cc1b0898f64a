{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3419,3521,3624,3726,3830,3933,4034,10516", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3516,3619,3721,3825,3928,4029,4151,10612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,428,534,645,736,841,963,1041,1116,1207,1300,1401,1495,1595,1689,1784,1883,1974,2065,2147,2256,2360,2459,2571,2683,2804,2969,10185", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "423,529,640,731,836,958,1036,1111,1202,1295,1396,1490,1590,1684,1779,1878,1969,2060,2142,2251,2355,2454,2566,2678,2799,2964,3065,10263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,351,434,521,620,714,824,916,978,1043,1142,1208,1268,1353,1455,1517,1593,1651,1729,1794,1866,1939,1996,2050,2167,2231,2295,2349,2429,2563,2649,2736,2839,2935,3024,3160,3245,3333,3485,3580,3663,3721,3773,3839,3918,4000,4071,4158,4234,4311,4388,4459,4569,4676,4756,4853,4953,5027,5108,5213,5271,5359,5426,5517,5609,5671,5735,5798,5867,5970,6062,6167,6257,6349,6447,6509,6565,6649,6743,6821", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,79,82,86,98,93,109,91,61,64,98,65,59,84,101,61,75,57,77,64,71,72,56,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,91,104,89,91,97,61,55,83,93,77,75", "endOffsets": "266,346,429,516,615,709,819,911,973,1038,1137,1203,1263,1348,1450,1512,1588,1646,1724,1789,1861,1934,1991,2045,2162,2226,2290,2344,2424,2558,2644,2731,2834,2930,3019,3155,3240,3328,3480,3575,3658,3716,3768,3834,3913,3995,4066,4153,4229,4306,4383,4454,4564,4671,4751,4848,4948,5022,5103,5208,5266,5354,5421,5512,5604,5666,5730,5793,5862,5965,6057,6162,6252,6344,6442,6504,6560,6644,6738,6816,6892"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3150,3233,3320,4156,4250,4360,4452,4514,4579,4678,4744,4804,4889,4991,5053,5129,5187,5265,5330,5402,5475,5532,5586,5703,5767,5831,5885,5965,6099,6185,6272,6375,6471,6560,6696,6781,6869,7021,7116,7199,7257,7309,7375,7454,7536,7607,7694,7770,7847,7924,7995,8105,8212,8292,8389,8489,8563,8644,8749,8807,8895,8962,9053,9145,9207,9271,9334,9403,9506,9598,9703,9793,9885,9983,10045,10101,10268,10362,10440", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,79,82,86,98,93,109,91,61,64,98,65,59,84,101,61,75,57,77,64,71,72,56,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,91,104,89,91,97,61,55,83,93,77,75", "endOffsets": "316,3145,3228,3315,3414,4245,4355,4447,4509,4574,4673,4739,4799,4884,4986,5048,5124,5182,5260,5325,5397,5470,5527,5581,5698,5762,5826,5880,5960,6094,6180,6267,6370,6466,6555,6691,6776,6864,7016,7111,7194,7252,7304,7370,7449,7531,7602,7689,7765,7842,7919,7990,8100,8207,8287,8384,8484,8558,8639,8744,8802,8890,8957,9048,9140,9202,9266,9329,9398,9501,9593,9698,9788,9880,9978,10040,10096,10180,10357,10435,10511"}}]}]}