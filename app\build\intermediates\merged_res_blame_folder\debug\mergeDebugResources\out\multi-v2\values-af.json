{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3323,3421,3523,3621,3719,3826,3935,10044", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3416,3518,3616,3714,3821,3930,4050,10140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,432,528,634,719,822,940,1017,1093,1184,1277,1372,1466,1565,1658,1753,1852,1947,2041,2122,2229,2334,2431,2539,2642,2744,2898,9731", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "427,523,629,714,817,935,1012,1088,1179,1272,1367,1461,1560,1653,1748,1847,1942,2036,2117,2224,2329,2426,2534,2637,2739,2893,2991,9807"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,601,701,815,896,956,1020,1108,1174,1237,1316,1402,1464,1525,1583,1649,1712,1776,1843,1900,1955,2073,2130,2192,2247,2316,2435,2523,2598,2691,2776,2859,2998,3081,3162,3290,3377,3454,3512,3563,3629,3698,3774,3845,3921,3995,4074,4147,4218,4321,4408,4479,4568,4658,4730,4805,4892,4943,5022,5089,5170,5254,5316,5380,5443,5513,5617,5710,5806,5896,5991,6083,6145,6200,6277,6360,6436", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,80,79,77,87,99,113,80,59,63,87,65,62,78,85,61,60,57,65,62,63,66,56,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,92,95,89,94,91,61,54,76,82,75,72", "endOffsets": "269,350,430,508,596,696,810,891,951,1015,1103,1169,1232,1311,1397,1459,1520,1578,1644,1707,1771,1838,1895,1950,2068,2125,2187,2242,2311,2430,2518,2593,2686,2771,2854,2993,3076,3157,3285,3372,3449,3507,3558,3624,3693,3769,3840,3916,3990,4069,4142,4213,4316,4403,4474,4563,4653,4725,4800,4887,4938,5017,5084,5165,5249,5311,5375,5438,5508,5612,5705,5801,5891,5986,6078,6140,6195,6272,6355,6431,6504"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2996,3077,3157,3235,4055,4155,4269,4350,4410,4474,4562,4628,4691,4770,4856,4918,4979,5037,5103,5166,5230,5297,5354,5409,5527,5584,5646,5701,5770,5889,5977,6052,6145,6230,6313,6452,6535,6616,6744,6831,6908,6966,7017,7083,7152,7228,7299,7375,7449,7528,7601,7672,7775,7862,7933,8022,8112,8184,8259,8346,8397,8476,8543,8624,8708,8770,8834,8897,8967,9071,9164,9260,9350,9445,9537,9599,9654,9812,9895,9971", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,80,79,77,87,99,113,80,59,63,87,65,62,78,85,61,60,57,65,62,63,66,56,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,92,95,89,94,91,61,54,76,82,75,72", "endOffsets": "319,3072,3152,3230,3318,4150,4264,4345,4405,4469,4557,4623,4686,4765,4851,4913,4974,5032,5098,5161,5225,5292,5349,5404,5522,5579,5641,5696,5765,5884,5972,6047,6140,6225,6308,6447,6530,6611,6739,6826,6903,6961,7012,7078,7147,7223,7294,7370,7444,7523,7596,7667,7770,7857,7928,8017,8107,8179,8254,8341,8392,8471,8538,8619,8703,8765,8829,8892,8962,9066,9159,9255,9345,9440,9532,9594,9649,9726,9890,9966,10039"}}]}]}