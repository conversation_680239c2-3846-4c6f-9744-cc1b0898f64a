{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "39,40,41,42,43,44,45,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3451,3547,3649,3750,3848,3958,4066,10302", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3542,3644,3745,3843,3953,4061,4183,10398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "428,535,636,747,833,941,1059,1138,1215,1306,1399,1497,1591,1691,1784,1879,1977,2068,2159,2243,2348,2456,2555,2661,2773,2876,3042,9985", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "530,631,742,828,936,1054,1133,1210,1301,1394,1492,1586,1686,1779,1874,1972,2063,2154,2238,2343,2451,2550,2656,2768,2871,3037,3135,10063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,689,781,909,990,1051,1116,1215,1291,1356,1437,1527,1591,1657,1711,1780,1840,1909,1982,2040,2094,2211,2271,2333,2387,2459,2589,2676,2756,2852,2936,3028,3167,3236,3314,3445,3533,3613,3667,3718,3784,3856,3933,4004,4086,4158,4235,4308,4379,4484,4572,4644,4736,4832,4906,4980,5076,5128,5210,5277,5364,5451,5513,5577,5640,5708,5814,5910,6008,6101,6199,6294,6352,6407,6486,6569,6644", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,74,74,77,82,91,127,80,60,64,98,75,64,80,89,63,65,53,68,59,68,72,57,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,95,97,92,97,94,57,54,78,82,74,75", "endOffsets": "373,448,523,601,684,776,904,985,1046,1111,1210,1286,1351,1432,1522,1586,1652,1706,1775,1835,1904,1977,2035,2089,2206,2266,2328,2382,2454,2584,2671,2751,2847,2931,3023,3162,3231,3309,3440,3528,3608,3662,3713,3779,3851,3928,3999,4081,4153,4230,4303,4374,4479,4567,4639,4731,4827,4901,4975,5071,5123,5205,5272,5359,5446,5508,5572,5635,5703,5809,5905,6003,6096,6194,6289,6347,6402,6481,6564,6639,6715"}, "to": {"startLines": "2,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3140,3215,3290,3368,4188,4280,4408,4489,4550,4615,4714,4790,4855,4936,5026,5090,5156,5210,5279,5339,5408,5481,5539,5593,5710,5770,5832,5886,5958,6088,6175,6255,6351,6435,6527,6666,6735,6813,6944,7032,7112,7166,7217,7283,7355,7432,7503,7585,7657,7734,7807,7878,7983,8071,8143,8235,8331,8405,8479,8575,8627,8709,8776,8863,8950,9012,9076,9139,9207,9313,9409,9507,9600,9698,9793,9851,9906,10068,10151,10226", "endLines": "7,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "endColumns": "12,74,74,77,82,91,127,80,60,64,98,75,64,80,89,63,65,53,68,59,68,72,57,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,95,97,92,97,94,57,54,78,82,74,75", "endOffsets": "423,3210,3285,3363,3446,4275,4403,4484,4545,4610,4709,4785,4850,4931,5021,5085,5151,5205,5274,5334,5403,5476,5534,5588,5705,5765,5827,5881,5953,6083,6170,6250,6346,6430,6522,6661,6730,6808,6939,7027,7107,7161,7212,7278,7350,7427,7498,7580,7652,7729,7802,7873,7978,8066,8138,8230,8326,8400,8474,8570,8622,8704,8771,8858,8945,9007,9071,9134,9202,9308,9404,9502,9595,9693,9788,9846,9901,9980,10146,10221,10297"}}]}]}