{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,9691", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,9768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,599,698,811,891,959,1029,1119,1189,1249,1328,1415,1481,1546,1607,1671,1732,1804,1879,1933,1987,2088,2149,2209,2263,2333,2444,2531,2608,2695,2777,2858,3001,3080,3162,3294,3386,3464,3518,3571,3637,3707,3785,3856,3936,4008,4086,4155,4224,4322,4404,4492,4585,4679,4753,4822,4917,4969,5052,5120,5205,5293,5355,5419,5482,5552,5652,5740,5837,5922,6015,6105,6163,6220,6297,6379,6454", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,72,71,82,85,98,112,79,67,69,89,69,59,78,86,65,64,60,63,60,71,74,53,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,87,96,84,92,89,57,56,76,81,74,75", "endOffsets": "280,353,425,508,594,693,806,886,954,1024,1114,1184,1244,1323,1410,1476,1541,1602,1666,1727,1799,1874,1928,1982,2083,2144,2204,2258,2328,2439,2526,2603,2690,2772,2853,2996,3075,3157,3289,3381,3459,3513,3566,3632,3702,3780,3851,3931,4003,4081,4150,4219,4317,4399,4487,4580,4674,4748,4817,4912,4964,5047,5115,5200,5288,5350,5414,5477,5547,5647,5735,5832,5917,6010,6100,6158,6215,6292,6374,6449,6525"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3993,4092,4205,4285,4353,4423,4513,4583,4643,4722,4809,4875,4940,5001,5065,5126,5198,5273,5327,5381,5482,5543,5603,5657,5727,5838,5925,6002,6089,6171,6252,6395,6474,6556,6688,6780,6858,6912,6965,7031,7101,7179,7250,7330,7402,7480,7549,7618,7716,7798,7886,7979,8073,8147,8216,8311,8363,8446,8514,8599,8687,8749,8813,8876,8946,9046,9134,9231,9316,9409,9499,9557,9614,9773,9855,9930", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,72,71,82,85,98,112,79,67,69,89,69,59,78,86,65,64,60,63,60,71,74,53,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,87,96,84,92,89,57,56,76,81,74,75", "endOffsets": "330,3032,3104,3187,3273,4087,4200,4280,4348,4418,4508,4578,4638,4717,4804,4870,4935,4996,5060,5121,5193,5268,5322,5376,5477,5538,5598,5652,5722,5833,5920,5997,6084,6166,6247,6390,6469,6551,6683,6775,6853,6907,6960,7026,7096,7174,7245,7325,7397,7475,7544,7613,7711,7793,7881,7974,8068,8142,8211,8306,8358,8441,8509,8594,8682,8744,8808,8871,8941,9041,9129,9226,9311,9404,9494,9552,9609,9686,9850,9925,10001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3278,3374,3477,3575,3673,3776,3881,10006", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3369,3472,3570,3668,3771,3876,3988,10102"}}]}]}