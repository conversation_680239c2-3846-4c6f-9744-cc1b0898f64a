{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,9808", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,9888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3322,3419,3521,3620,3720,3823,3936,10128", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3414,3516,3615,3715,3818,3931,4047,10224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,678,792,872,935,998,1092,1166,1225,1303,1389,1451,1512,1570,1634,1695,1775,1856,1914,1968,2085,2142,2202,2256,2331,2458,2542,2622,2717,2801,2879,3009,3093,3171,3305,3396,3477,3528,3579,3645,3713,3789,3860,3940,4019,4094,4167,4243,4349,4438,4515,4606,4700,4774,4844,4937,4986,5067,5133,5218,5304,5366,5430,5493,5564,5663,5760,5858,5955,6051,6147,6202,6257,6335,6417,6496", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,78,76,77,79,98,113,79,62,62,93,73,58,77,85,61,60,57,63,60,79,80,57,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,96,97,96,95,95,54,54,77,81,78,73", "endOffsets": "260,339,416,494,574,673,787,867,930,993,1087,1161,1220,1298,1384,1446,1507,1565,1629,1690,1770,1851,1909,1963,2080,2137,2197,2251,2326,2453,2537,2617,2712,2796,2874,3004,3088,3166,3300,3391,3472,3523,3574,3640,3708,3784,3855,3935,4014,4089,4162,4238,4344,4433,4510,4601,4695,4769,4839,4932,4981,5062,5128,5213,5299,5361,5425,5488,5559,5658,5755,5853,5950,6046,6142,6197,6252,6330,6412,6491,6565"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3008,3087,3164,3242,4052,4151,4265,4345,4408,4471,4565,4639,4698,4776,4862,4924,4985,5043,5107,5168,5248,5329,5387,5441,5558,5615,5675,5729,5804,5931,6015,6095,6190,6274,6352,6482,6566,6644,6778,6869,6950,7001,7052,7118,7186,7262,7333,7413,7492,7567,7640,7716,7822,7911,7988,8079,8173,8247,8317,8410,8459,8540,8606,8691,8777,8839,8903,8966,9037,9136,9233,9331,9428,9524,9620,9675,9730,9893,9975,10054", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,78,76,77,79,98,113,79,62,62,93,73,58,77,85,61,60,57,63,60,79,80,57,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,96,97,96,95,95,54,54,77,81,78,73", "endOffsets": "310,3082,3159,3237,3317,4146,4260,4340,4403,4466,4560,4634,4693,4771,4857,4919,4980,5038,5102,5163,5243,5324,5382,5436,5553,5610,5670,5724,5799,5926,6010,6090,6185,6269,6347,6477,6561,6639,6773,6864,6945,6996,7047,7113,7181,7257,7328,7408,7487,7562,7635,7711,7817,7906,7983,8074,8168,8242,8312,8405,8454,8535,8601,8686,8772,8834,8898,8961,9032,9131,9228,9326,9423,9519,9615,9670,9725,9803,9970,10049,10123"}}]}]}