{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,786,887,1006,1070,1147,1206,1269,1360,1429,1496,1578,1678,1741,1806,1867,1935,1997,2064,2133,2190,2248,2362,2422,2483,2540,2613,2736,2817,2909,3016,3114,3194,3342,3423,3504,3632,3721,3797,3850,3904,3970,4048,4128,4199,4281,4353,4427,4500,4570,4679,4770,4841,4931,5026,5100,5183,5276,5325,5406,5475,5561,5646,5708,5772,5835,5904,6013,6110,6207,6300,6392,6490,6547,6605,6685,6764,6839", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90", "endColumns": "12,77,75,83,82,100,118,63,76,58,62,90,68,66,81,99,62,64,60,67,61,66,68,56,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,96,96,92,91,97,56,57,79,78,74,75", "endOffsets": "460,538,614,698,781,882,1001,1065,1142,1201,1264,1355,1424,1491,1573,1673,1736,1801,1862,1930,1992,2059,2128,2185,2243,2357,2417,2478,2535,2608,2731,2812,2904,3011,3109,3189,3337,3418,3499,3627,3716,3792,3845,3899,3965,4043,4123,4194,4276,4348,4422,4495,4565,4674,4765,4836,4926,5021,5095,5178,5271,5320,5401,5470,5556,5641,5703,5767,5830,5899,6008,6105,6202,6295,6387,6485,6542,6600,6680,6759,6834,6910"}, "to": {"startLines": "2,37,38,39,40,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3169,3247,3323,3407,4202,4303,4422,4486,4563,4622,4685,4776,4845,4912,4994,5094,5157,5222,5283,5351,5413,5480,5549,5606,5664,5778,5838,5899,5956,6029,6152,6233,6325,6432,6530,6610,6758,6839,6920,7048,7137,7213,7266,7320,7386,7464,7544,7615,7697,7769,7843,7916,7986,8095,8186,8257,8347,8442,8516,8599,8692,8741,8822,8891,8977,9062,9124,9188,9251,9320,9429,9526,9623,9716,9808,9906,9963,10021,10183,10262,10337", "endLines": "9,37,38,39,40,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,123,124,125", "endColumns": "12,77,75,83,82,100,118,63,76,58,62,90,68,66,81,99,62,64,60,67,61,66,68,56,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,96,96,92,91,97,56,57,79,78,74,75", "endOffsets": "510,3242,3318,3402,3485,4298,4417,4481,4558,4617,4680,4771,4840,4907,4989,5089,5152,5217,5278,5346,5408,5475,5544,5601,5659,5773,5833,5894,5951,6024,6147,6228,6320,6427,6525,6605,6753,6834,6915,7043,7132,7208,7261,7315,7381,7459,7539,7610,7692,7764,7838,7911,7981,8090,8181,8252,8342,8437,8511,8594,8687,8736,8817,8886,8972,9057,9119,9183,9246,9315,9424,9521,9618,9711,9803,9901,9958,10016,10096,10257,10332,10408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,623,727,834,916,1017,1131,1211,1290,1381,1474,1566,1660,1760,1853,1948,2041,2132,2226,2305,2410,2508,2606,2714,2814,2917,3072,10101", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "618,722,829,911,1012,1126,1206,1285,1376,1469,1561,1655,1755,1848,1943,2036,2127,2221,2300,2405,2503,2601,2709,2809,2912,3067,3164,10178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "41,42,43,44,45,46,47,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3490,3583,3685,3780,3883,3986,4088,10413", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "3578,3680,3775,3878,3981,4083,4197,10509"}}]}]}