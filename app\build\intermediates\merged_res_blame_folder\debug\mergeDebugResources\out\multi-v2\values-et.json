{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3369,3464,3566,3664,3767,3873,3978,10183", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3459,3561,3659,3762,3868,3973,4093,10279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,597,696,813,895,955,1019,1104,1172,1236,1315,1402,1466,1530,1589,1661,1725,1793,1864,1922,1976,2095,2155,2216,2270,2343,2476,2560,2637,2730,2810,2903,3041,3121,3200,3326,3414,3493,3548,3599,3665,3738,3817,3888,3967,4040,4115,4189,4261,4374,4462,4539,4630,4722,4796,4870,4961,5015,5097,5166,5249,5335,5397,5461,5524,5592,5695,5786,5883,5982,6072,6160,6219,6274,6355,6444,6521", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,79,78,84,86,98,116,81,59,63,84,67,63,78,86,63,63,58,71,63,67,70,57,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,90,96,98,89,87,58,54,80,88,76,77", "endOffsets": "261,341,420,505,592,691,808,890,950,1014,1099,1167,1231,1310,1397,1461,1525,1584,1656,1720,1788,1859,1917,1971,2090,2150,2211,2265,2338,2471,2555,2632,2725,2805,2898,3036,3116,3195,3321,3409,3488,3543,3594,3660,3733,3812,3883,3962,4035,4110,4184,4256,4369,4457,4534,4625,4717,4791,4865,4956,5010,5092,5161,5244,5330,5392,5456,5519,5587,5690,5781,5878,5977,6067,6155,6214,6269,6350,6439,6516,6594"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3038,3118,3197,3282,4098,4197,4314,4396,4456,4520,4605,4673,4737,4816,4903,4967,5031,5090,5162,5226,5294,5365,5423,5477,5596,5656,5717,5771,5844,5977,6061,6138,6231,6311,6404,6542,6622,6701,6827,6915,6994,7049,7100,7166,7239,7318,7389,7468,7541,7616,7690,7762,7875,7963,8040,8131,8223,8297,8371,8462,8516,8598,8667,8750,8836,8898,8962,9025,9093,9196,9287,9384,9483,9573,9661,9720,9775,9939,10028,10105", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,79,78,84,86,98,116,81,59,63,84,67,63,78,86,63,63,58,71,63,67,70,57,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,90,96,98,89,87,58,54,80,88,76,77", "endOffsets": "311,3113,3192,3277,3364,4192,4309,4391,4451,4515,4600,4668,4732,4811,4898,4962,5026,5085,5157,5221,5289,5360,5418,5472,5591,5651,5712,5766,5839,5972,6056,6133,6226,6306,6399,6537,6617,6696,6822,6910,6989,7044,7095,7161,7234,7313,7384,7463,7536,7611,7685,7757,7870,7958,8035,8126,8218,8292,8366,8457,8511,8593,8662,8745,8831,8893,8957,9020,9088,9191,9282,9379,9478,9568,9656,9715,9770,9851,10023,10100,10178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,422,521,632,718,820,937,1018,1095,1187,1281,1377,1479,1588,1682,1783,1877,1969,2062,2145,2256,2360,2459,2569,2671,2770,2936,9856", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "417,516,627,713,815,932,1013,1090,1182,1276,1372,1474,1583,1677,1778,1872,1964,2057,2140,2251,2355,2454,2564,2666,2765,2931,3033,9934"}}]}]}