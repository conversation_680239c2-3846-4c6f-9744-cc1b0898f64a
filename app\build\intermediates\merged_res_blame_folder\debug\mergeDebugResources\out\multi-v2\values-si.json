{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,345,422,500,585,687,802,885,946,1010,1099,1166,1226,1304,1398,1462,1525,1581,1651,1718,1783,1851,1905,1960,2079,2136,2200,2254,2327,2449,2532,2615,2708,2794,2879,3011,3089,3169,3291,3377,3461,3521,3573,3639,3709,3782,3853,3930,4002,4079,4151,4221,4334,4427,4500,4590,4683,4757,4829,4920,4974,5054,5120,5204,5289,5351,5415,5478,5544,5649,5739,5834,5919,6012,6100,6164,6220,6300,6385,6460", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,75,76,77,84,101,114,82,60,63,88,66,59,77,93,63,62,55,69,66,64,67,53,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,89,94,84,92,87,63,55,79,84,74,75", "endOffsets": "264,340,417,495,580,682,797,880,941,1005,1094,1161,1221,1299,1393,1457,1520,1576,1646,1713,1778,1846,1900,1955,2074,2131,2195,2249,2322,2444,2527,2610,2703,2789,2874,3006,3084,3164,3286,3372,3456,3516,3568,3634,3704,3777,3848,3925,3997,4074,4146,4216,4329,4422,4495,4585,4678,4752,4824,4915,4969,5049,5115,5199,5284,5346,5410,5473,5539,5644,5734,5829,5914,6007,6095,6159,6215,6295,6380,6455,6531"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3035,3111,3188,3266,4083,4185,4300,4383,4444,4508,4597,4664,4724,4802,4896,4960,5023,5079,5149,5216,5281,5349,5403,5458,5577,5634,5698,5752,5825,5947,6030,6113,6206,6292,6377,6509,6587,6667,6789,6875,6959,7019,7071,7137,7207,7280,7351,7428,7500,7577,7649,7719,7832,7925,7998,8088,8181,8255,8327,8418,8472,8552,8618,8702,8787,8849,8913,8976,9042,9147,9237,9332,9417,9510,9598,9662,9718,9880,9965,10040", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,75,76,77,84,101,114,82,60,63,88,66,59,77,93,63,62,55,69,66,64,67,53,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,89,94,84,92,87,63,55,79,84,74,75", "endOffsets": "314,3106,3183,3261,3346,4180,4295,4378,4439,4503,4592,4659,4719,4797,4891,4955,5018,5074,5144,5211,5276,5344,5398,5453,5572,5629,5693,5747,5820,5942,6025,6108,6201,6287,6372,6504,6582,6662,6784,6870,6954,7014,7066,7132,7202,7275,7346,7423,7495,7572,7644,7714,7827,7920,7993,8083,8176,8250,8322,8413,8467,8547,8613,8697,8782,8844,8908,8971,9037,9142,9232,9327,9412,9505,9593,9657,9713,9793,9960,10035,10111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,435,542,649,732,837,953,1043,1129,1220,1313,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2362,2460,2570,2670,2777,2936,9798", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "430,537,644,727,832,948,1038,1124,1215,1308,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2357,2455,2565,2665,2772,2931,3030,9875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3351,3453,3556,3661,3766,3865,3969,10116", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3448,3551,3656,3761,3860,3964,4078,10212"}}]}]}