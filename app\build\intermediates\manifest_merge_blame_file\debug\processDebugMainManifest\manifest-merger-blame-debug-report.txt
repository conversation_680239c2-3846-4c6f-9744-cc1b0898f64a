1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.th2_1"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb5f6f8bf2c7a7ef52f138f50dee19f9\transformed\core-1.17.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.th2_1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb5f6f8bf2c7a7ef52f138f50dee19f9\transformed\core-1.17.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb5f6f8bf2c7a7ef52f138f50dee19f9\transformed\core-1.17.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.th2_1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb5f6f8bf2c7a7ef52f138f50dee19f9\transformed\core-1.17.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb5f6f8bf2c7a7ef52f138f50dee19f9\transformed\core-1.17.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:5:5-26:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.17.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb5f6f8bf2c7a7ef52f138f50dee19f9\transformed\core-1.17.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.TH2_1" >
29-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:13:9-43
30        <activity
30-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:14:9-22:20
31            android:name="com.example.th2_1.MainActivity"
31-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:15:13-41
32            android:exported="true" >
32-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:16:13-36
33            <intent-filter>
33-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:17:13-21:29
34                <action android:name="android.intent.action.MAIN" />
34-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:18:17-69
34-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:18:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:20:17-77
36-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:20:27-74
37            </intent-filter>
38        </activity>
39        <activity
39-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:23:9-25:40
40            android:name="com.example.th2_1.NumberSelectionActivity"
40-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:24:13-52
41            android:exported="false" />
41-->C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\AndroidManifest.xml:25:13-37
42
43        <provider
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4c898b749837c45faf546dcf3d424a1\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
44            android:name="androidx.startup.InitializationProvider"
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4c898b749837c45faf546dcf3d424a1\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
45            android:authorities="com.example.th2_1.androidx-startup"
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4c898b749837c45faf546dcf3d424a1\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
46            android:exported="false" >
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4c898b749837c45faf546dcf3d424a1\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
47            <meta-data
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4c898b749837c45faf546dcf3d424a1\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.emoji2.text.EmojiCompatInitializer"
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4c898b749837c45faf546dcf3d424a1\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
49                android:value="androidx.startup" />
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4c898b749837c45faf546dcf3d424a1\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3763f261812a65b837d2ae77601bdce\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
51-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3763f261812a65b837d2ae77601bdce\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
52                android:value="androidx.startup" />
52-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3763f261812a65b837d2ae77601bdce\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
53            <meta-data
53-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
54-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
55                android:value="androidx.startup" />
55-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
56        </provider>
57
58        <receiver
58-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
59            android:name="androidx.profileinstaller.ProfileInstallReceiver"
59-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
60            android:directBootAware="false"
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
61            android:enabled="true"
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
62            android:exported="true"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
63            android:permission="android.permission.DUMP" >
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
64            <intent-filter>
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
65                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
66            </intent-filter>
67            <intent-filter>
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
68                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
69            </intent-filter>
70            <intent-filter>
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
71                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
72            </intent-filter>
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
74                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
75            </intent-filter>
76        </receiver>
77    </application>
78
79</manifest>
