{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,7,8,9,268,269,270,271,272,275", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,559,623,690,19041,19157,19283,19409,19537,19709", "endLines": "2,7,8,9,268,269,270,271,274,279", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,618,685,749,19152,19278,19404,19532,19704,20056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,248,344", "endColumns": "97,94,95,96", "endOffsets": "148,243,339,436"}, "to": {"startLines": "3,4,5,6", "startColumns": "4,4,4,4", "startOffsets": "173,271,366,462", "endColumns": "97,94,95,96", "endOffsets": "266,361,457,554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,27,28,29,30,32,34,35,36,37,38,40,42,44,46,48,50,51,56,58,60,61,62,64,66,67,68,69,70,71,114,117,160,163,166,168,170,172,175,179,182,183,184,187,188,189,190,191,192,195,196,198,200,202,204,208,210,211,212,213,215,219,221,223,224,225,226,227,228,230,231,232,242,243,244,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "754,845,948,1051,1156,1263,1372,1481,1590,1699,1808,1915,2018,2137,2292,2447,2552,2673,2774,2921,3062,3165,3284,3391,3494,3649,3820,3969,4134,4291,4442,4561,4912,5061,5210,5322,5469,5622,5769,5844,5933,6020,6121,6224,8982,9167,11937,12134,12333,12456,12579,12692,12875,13130,13331,13420,13531,13764,13865,13960,14083,14212,14329,14506,14605,14740,14883,15018,15137,15338,15457,15550,15661,15717,15824,16019,16130,16263,16358,16449,16540,16633,16750,16889,16960,17043,17666,17723,17781,18405", "endLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,24,26,27,28,29,31,33,34,35,36,37,39,41,43,45,47,49,50,55,57,59,60,61,63,65,66,67,68,69,70,113,116,159,162,165,167,169,171,174,178,181,182,183,186,187,188,189,190,191,194,195,197,199,201,203,207,209,210,211,212,214,218,220,222,223,224,225,226,227,229,230,231,241,242,243,255,267", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "840,943,1046,1151,1258,1367,1476,1585,1694,1803,1910,2013,2132,2287,2442,2547,2668,2769,2916,3057,3160,3279,3386,3489,3644,3815,3964,4129,4286,4437,4556,4907,5056,5205,5317,5464,5617,5764,5839,5928,6015,6116,6219,8977,9162,11932,12129,12328,12451,12574,12687,12870,13125,13326,13415,13526,13759,13860,13955,14078,14207,14324,14501,14600,14735,14878,15013,15132,15333,15452,15545,15656,15712,15819,16014,16125,16258,16353,16444,16535,16628,16745,16884,16955,17038,17661,17718,17776,18400,19036"}}]}]}