{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,590,697,829,912,979,1044,1138,1207,1266,1349,1434,1497,1560,1618,1683,1744,1811,1882,1939,2000,2106,2164,2224,2283,2353,2469,2548,2639,2732,2830,2910,3044,3119,3195,3332,3429,3527,3584,3639,3705,3775,3852,3923,4008,4076,4152,4233,4311,4412,4498,4585,4682,4781,4855,4925,5029,5083,5170,5237,5327,5419,5481,5545,5608,5674,5779,5870,5971,6062,6158,6254,6315,6374,6453,6538,6618", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,77,75,80,95,106,131,82,66,64,93,68,58,82,84,62,62,57,64,60,66,70,56,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,90,100,90,95,95,60,58,78,84,79,72", "endOffsets": "254,332,408,489,585,692,824,907,974,1039,1133,1202,1261,1344,1429,1492,1555,1613,1678,1739,1806,1877,1934,1995,2101,2159,2219,2278,2348,2464,2543,2634,2727,2825,2905,3039,3114,3190,3327,3424,3522,3579,3634,3700,3770,3847,3918,4003,4071,4147,4228,4306,4407,4493,4580,4677,4776,4850,4920,5024,5078,5165,5232,5322,5414,5476,5540,5603,5669,5774,5865,5966,6057,6153,6249,6310,6369,6448,6533,6613,6686"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2991,3069,3145,3226,4075,4182,4314,4397,4464,4529,4623,4692,4751,4834,4919,4982,5045,5103,5168,5229,5296,5367,5424,5485,5591,5649,5709,5768,5838,5954,6033,6124,6217,6315,6395,6529,6604,6680,6817,6914,7012,7069,7124,7190,7260,7337,7408,7493,7561,7637,7718,7796,7897,7983,8070,8167,8266,8340,8410,8514,8568,8655,8722,8812,8904,8966,9030,9093,9159,9264,9355,9456,9547,9643,9739,9800,9859,10020,10105,10185", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,77,75,80,95,106,131,82,66,64,93,68,58,82,84,62,62,57,64,60,66,70,56,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,90,100,90,95,95,60,58,78,84,79,72", "endOffsets": "304,3064,3140,3221,3317,4177,4309,4392,4459,4524,4618,4687,4746,4829,4914,4977,5040,5098,5163,5224,5291,5362,5419,5480,5586,5644,5704,5763,5833,5949,6028,6119,6212,6310,6390,6524,6599,6675,6812,6909,7007,7064,7119,7185,7255,7332,7403,7488,7556,7632,7713,7791,7892,7978,8065,8162,8261,8335,8405,8509,8563,8650,8717,8807,8899,8961,9025,9088,9154,9259,9350,9451,9542,9638,9734,9795,9854,9933,10100,10180,10253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,9938", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,10015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3322,3420,3523,3628,3729,3842,3948,10258", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3415,3518,3623,3724,3837,3943,4070,10354"}}]}]}