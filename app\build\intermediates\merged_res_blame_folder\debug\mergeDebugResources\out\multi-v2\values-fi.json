{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,9765", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,9841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3303,3399,3501,3599,3704,3809,3921,10086", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3394,3496,3594,3699,3804,3916,4032,10182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,583,679,787,871,933,998,1091,1166,1231,1309,1397,1462,1528,1586,1657,1723,1790,1858,1913,1967,2077,2137,2201,2255,2328,2444,2528,2604,2695,2776,2857,2990,3075,3160,3293,3383,3457,3509,3560,3626,3703,3785,3856,3930,4004,4083,4160,4232,4339,4428,4504,4595,4690,4764,4837,4931,4985,5059,5131,5217,5303,5365,5429,5492,5563,5664,5753,5848,5934,6029,6121,6177,6232,6311,6397,6476", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,75,73,82,81,95,107,83,61,64,92,74,64,77,87,64,65,57,70,65,66,67,54,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,88,94,85,94,91,55,54,78,85,78,74", "endOffsets": "263,339,413,496,578,674,782,866,928,993,1086,1161,1226,1304,1392,1457,1523,1581,1652,1718,1785,1853,1908,1962,2072,2132,2196,2250,2323,2439,2523,2599,2690,2771,2852,2985,3070,3155,3288,3378,3452,3504,3555,3621,3698,3780,3851,3925,3999,4078,4155,4227,4334,4423,4499,4590,4685,4759,4832,4926,4980,5054,5126,5212,5298,5360,5424,5487,5558,5659,5748,5843,5929,6024,6116,6172,6227,6306,6392,6471,6546"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2988,3064,3138,3221,4037,4133,4241,4325,4387,4452,4545,4620,4685,4763,4851,4916,4982,5040,5111,5177,5244,5312,5367,5421,5531,5591,5655,5709,5782,5898,5982,6058,6149,6230,6311,6444,6529,6614,6747,6837,6911,6963,7014,7080,7157,7239,7310,7384,7458,7537,7614,7686,7793,7882,7958,8049,8144,8218,8291,8385,8439,8513,8585,8671,8757,8819,8883,8946,9017,9118,9207,9302,9388,9483,9575,9631,9686,9846,9932,10011", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,75,73,82,81,95,107,83,61,64,92,74,64,77,87,64,65,57,70,65,66,67,54,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,88,94,85,94,91,55,54,78,85,78,74", "endOffsets": "313,3059,3133,3216,3298,4128,4236,4320,4382,4447,4540,4615,4680,4758,4846,4911,4977,5035,5106,5172,5239,5307,5362,5416,5526,5586,5650,5704,5777,5893,5977,6053,6144,6225,6306,6439,6524,6609,6742,6832,6906,6958,7009,7075,7152,7234,7305,7379,7453,7532,7609,7681,7788,7877,7953,8044,8139,8213,8286,8380,8434,8508,8580,8666,8752,8814,8878,8941,9012,9113,9202,9297,9383,9478,9570,9626,9681,9760,9927,10006,10081"}}]}]}