com.example.th2_1.app-emoji2-views-helper-1.3.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\06fd1761edbe865f1794348669493922\transformed\emoji2-views-helper-1.3.0\res
com.example.th2_1.app-core-runtime-2.2.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\212ff712a5e6f86bcf8bb7d21e6a4997\transformed\core-runtime-2.2.0\res
com.example.th2_1.app-core-ktx-1.17.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\2c0ed31e6915223a320f552a5d8cf61e\transformed\core-ktx-1.17.0\res
com.example.th2_1.app-recyclerview-1.2.1-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\3c2c6e681ec968933356b2e728b667b8\transformed\recyclerview-1.2.1\res
com.example.th2_1.app-lifecycle-livedata-2.6.2-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\3fca1800d68e842f653d08a247eb2cf9\transformed\lifecycle-livedata-2.6.2\res
com.example.th2_1.app-constraintlayout-2.2.1-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\485c23a834304a0df9b12dbfc47afda9\transformed\constraintlayout-2.2.1\res
com.example.th2_1.app-annotation-experimental-1.4.1-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\4b2146bc437fd5a21ae457aa38af13d6\transformed\annotation-experimental-1.4.1\res
com.example.th2_1.app-core-viewtree-1.0.0-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\5cb1204ea2d8713b4e0a41f92a0e311e\transformed\core-viewtree-1.0.0\res
com.example.th2_1.app-startup-runtime-1.1.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\5dc7beda3e29d76cd774784d545a0ffc\transformed\startup-runtime-1.1.1\res
com.example.th2_1.app-cardview-1.0.0-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\673179d8a407296d56bdf2dab35e4725\transformed\cardview-1.0.0\res
com.example.th2_1.app-appcompat-1.7.1-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\74e2cf8db6b03b348cce7b0ce514ee13\transformed\appcompat-1.7.1\res
com.example.th2_1.app-lifecycle-viewmodel-2.6.2-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\78f87d7914e704804f785bdb46eb4df0\transformed\lifecycle-viewmodel-2.6.2\res
com.example.th2_1.app-dynamicanimation-1.1.0-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\79d1b093a7974304c545fc727ce11722\transformed\dynamicanimation-1.1.0\res
com.example.th2_1.app-transition-1.5.0-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\7ed619a1d32fbd91b2ba64a66447e199\transformed\transition-1.5.0\res
com.example.th2_1.app-lifecycle-viewmodel-savedstate-2.6.2-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\804db9be4d55b4b15ac95dbc089156d9\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.example.th2_1.app-lifecycle-livedata-core-2.6.2-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\8d7696d9d505c8fc2d9898b0bea1dbf8\transformed\lifecycle-livedata-core-2.6.2\res
com.example.th2_1.app-coordinatorlayout-1.1.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\9aee476f8df27c245eff66d5ac8b465d\transformed\coordinatorlayout-1.1.0\res
com.example.th2_1.app-emoji2-1.3.0-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\a4c898b749837c45faf546dcf3d424a1\transformed\emoji2-1.3.0\res
com.example.th2_1.app-profileinstaller-1.4.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\ab84e5f65df9a9be409425061bd6916e\transformed\profileinstaller-1.4.0\res
com.example.th2_1.app-viewpager2-1.0.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\c4496151e51805a2c45dee789ea0c171\transformed\viewpager2-1.0.0\res
com.example.th2_1.app-material-1.13.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\c44c5e9b00e7cb770ebac8dfe17166cf\transformed\material-1.13.0\res
com.example.th2_1.app-core-1.17.0-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\cb5f6f8bf2c7a7ef52f138f50dee19f9\transformed\core-1.17.0\res
com.example.th2_1.app-drawerlayout-1.1.1-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\cfcb61ba1f0ea54fa14de58e7df8bc7a\transformed\drawerlayout-1.1.1\res
com.example.th2_1.app-appcompat-resources-1.7.1-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\d14e100d1c5c70d5e013074d4427d834\transformed\appcompat-resources-1.7.1\res
com.example.th2_1.app-tracing-1.2.0-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\d1d4a8f6389c50016ca4df8691e67287\transformed\tracing-1.2.0\res
com.example.th2_1.app-lifecycle-process-2.6.2-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\d3763f261812a65b837d2ae77601bdce\transformed\lifecycle-process-2.6.2\res
com.example.th2_1.app-fragment-1.5.4-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\d848c58550ffc785f1f0bb5e3e9e4abc\transformed\fragment-1.5.4\res
com.example.th2_1.app-lifecycle-runtime-2.6.2-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\dc39ba1258ad0943f68abd257b57c92e\transformed\lifecycle-runtime-2.6.2\res
com.example.th2_1.app-activity-1.11.0-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\e2a1fcba6d98e91becd5e4154f60fad8\transformed\activity-1.11.0\res
com.example.th2_1.app-graphics-shapes-release-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\eb3ae36a0d7d3ccf71f8c21071023c5f\transformed\graphics-shapes-release\res
com.example.th2_1.app-savedstate-1.2.1-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\ec5bb48199b6634e24ec390921396c07\transformed\savedstate-1.2.1\res
com.example.th2_1.app-pngs-31 C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\build\generated\res\pngs\debug
com.example.th2_1.app-resValues-32 C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\build\generated\res\resValues\debug
com.example.th2_1.app-updated_navigation_xml-33 C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\build\generated\updated_navigation_xml\debug
com.example.th2_1.app-packageDebugResources-34 C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.th2_1.app-packageDebugResources-35 C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.th2_1.app-debug-36 C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.th2_1.app-debug-37 C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\debug\res
com.example.th2_1.app-main-38 C:\Users\<USER>\Documents\Baitaptuan2\TH2_1\app\src\main\res
