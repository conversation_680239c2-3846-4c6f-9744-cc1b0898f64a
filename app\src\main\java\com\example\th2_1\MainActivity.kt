package com.example.th2_1

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat

class MainActivity : AppCompatActivity() {

    private lateinit var etNumberInput: EditText
    private lateinit var btnCreate: Button
    private lateinit var tvError: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        initViews()
        setupListeners()
    }

    private fun initViews() {
        etNumberInput = findViewById(R.id.etNumberInput)
        btnCreate = findViewById(R.id.btnCreate)
        tvError = findViewById(R.id.tvError)
    }

    private fun setupListeners() {
        btnCreate.setOnClickListener {
            val inputText = etNumberInput.text.toString().trim()

            // Hide error message initially
            tvError.visibility = View.GONE

            if (inputText.isEmpty()) {
                showError()
                return@setOnClickListener
            }

            try {
                val number = inputText.toInt()
                if (number > 0) {
                    // Valid number, navigate to NumberSelectionActivity
                    val intent = Intent(this, NumberSelectionActivity::class.java)
                    intent.putExtra("NUMBER", number)
                    startActivity(intent)
                } else {
                    // Invalid number (zero or negative)
                    showError()
                }
            } catch (e: NumberFormatException) {
                // Invalid input (not a number)
                showError()
            }
        }
    }

    private fun showError() {
        tvError.visibility = View.VISIBLE
    }
}