{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,447,552,659,742,848,974,1058,1137,1228,1321,1414,1509,1607,1700,1793,1887,1978,2069,2150,2261,2369,2467,2577,2682,2790,2950,10168", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "442,547,654,737,843,969,1053,1132,1223,1316,1409,1504,1602,1695,1788,1882,1973,2064,2145,2256,2364,2462,2572,2677,2785,2945,3044,10245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3390,3486,3588,3687,3784,3890,3995,10492", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "3481,3583,3682,3779,3885,3990,4116,10588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,615,716,844,928,989,1054,1151,1231,1296,1383,1478,1542,1614,1676,1752,1815,1887,1964,2020,2077,2198,2256,2317,2374,2454,2591,2678,2753,2846,2926,3010,3149,3227,3306,3458,3547,3623,3680,3736,3802,3880,3961,4032,4120,4198,4275,4349,4428,4538,4628,4720,4812,4913,4987,5069,5170,5220,5303,5369,5461,5548,5610,5674,5737,5810,5933,6036,6140,6238,6349,6455,6516,6576,6662,6748,6825", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,80,79,87,91,100,127,83,60,64,96,79,64,86,94,63,71,61,75,62,71,76,55,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,102,103,97,110,105,60,59,85,85,76,78", "endOffsets": "269,350,430,518,610,711,839,923,984,1049,1146,1226,1291,1378,1473,1537,1609,1671,1747,1810,1882,1959,2015,2072,2193,2251,2312,2369,2449,2586,2673,2748,2841,2921,3005,3144,3222,3301,3453,3542,3618,3675,3731,3797,3875,3956,4027,4115,4193,4270,4344,4423,4533,4623,4715,4807,4908,4982,5064,5165,5215,5298,5364,5456,5543,5605,5669,5732,5805,5928,6031,6135,6233,6344,6450,6511,6571,6657,6743,6820,6899"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3049,3130,3210,3298,4121,4222,4350,4434,4495,4560,4657,4737,4802,4889,4984,5048,5120,5182,5258,5321,5393,5470,5526,5583,5704,5762,5823,5880,5960,6097,6184,6259,6352,6432,6516,6655,6733,6812,6964,7053,7129,7186,7242,7308,7386,7467,7538,7626,7704,7781,7855,7934,8044,8134,8226,8318,8419,8493,8575,8676,8726,8809,8875,8967,9054,9116,9180,9243,9316,9439,9542,9646,9744,9855,9961,10022,10082,10250,10336,10413", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,80,79,87,91,100,127,83,60,64,96,79,64,86,94,63,71,61,75,62,71,76,55,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,102,103,97,110,105,60,59,85,85,76,78", "endOffsets": "319,3125,3205,3293,3385,4217,4345,4429,4490,4555,4652,4732,4797,4884,4979,5043,5115,5177,5253,5316,5388,5465,5521,5578,5699,5757,5818,5875,5955,6092,6179,6254,6347,6427,6511,6650,6728,6807,6959,7048,7124,7181,7237,7303,7381,7462,7533,7621,7699,7776,7850,7929,8039,8129,8221,8313,8414,8488,8570,8671,8721,8804,8870,8962,9049,9111,9175,9238,9311,9434,9537,9641,9739,9850,9956,10017,10077,10163,10331,10408,10487"}}]}]}