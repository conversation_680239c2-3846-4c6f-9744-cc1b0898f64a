{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,434,532,642,728,834,958,1044,1125,1217,1311,1407,1501,1602,1696,1792,1889,1981,2074,2156,2265,2374,2473,2582,2689,2800,2971,10019", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "429,527,637,723,829,953,1039,1120,1212,1306,1402,1496,1597,1691,1787,1884,1976,2069,2151,2260,2369,2468,2577,2684,2795,2966,3065,10097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3462,3560,3663,3763,3866,3971,4074,10341", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3555,3658,3758,3861,3966,4069,4188,10437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,667,765,880,963,1029,1096,1195,1263,1324,1403,1491,1554,1620,1684,1755,1818,1892,1968,2025,2079,2188,2247,2310,2364,2438,2563,2653,2731,2820,2903,2983,3128,3211,3293,3432,3523,3606,3658,3711,3777,3848,3928,3999,4079,4157,4235,4308,4383,4490,4577,4664,4755,4848,4920,4996,5088,5139,5221,5287,5371,5457,5519,5583,5646,5714,5821,5915,6011,6103,6201,6297,6353,6410,6493,6578,6655", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,106,104,79,99,97,114,82,65,66,98,67,60,78,87,62,65,63,70,62,73,75,56,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,138,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,93,95,91,97,95,55,56,82,84,76,76", "endOffsets": "270,377,482,562,662,760,875,958,1024,1091,1190,1258,1319,1398,1486,1549,1615,1679,1750,1813,1887,1963,2020,2074,2183,2242,2305,2359,2433,2558,2648,2726,2815,2898,2978,3123,3206,3288,3427,3518,3601,3653,3706,3772,3843,3923,3994,4074,4152,4230,4303,4378,4485,4572,4659,4750,4843,4915,4991,5083,5134,5216,5282,5366,5452,5514,5578,5641,5709,5816,5910,6006,6098,6196,6292,6348,6405,6488,6573,6650,6727"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3177,3282,3362,4193,4291,4406,4489,4555,4622,4721,4789,4850,4929,5017,5080,5146,5210,5281,5344,5418,5494,5551,5605,5714,5773,5836,5890,5964,6089,6179,6257,6346,6429,6509,6654,6737,6819,6958,7049,7132,7184,7237,7303,7374,7454,7525,7605,7683,7761,7834,7909,8016,8103,8190,8281,8374,8446,8522,8614,8665,8747,8813,8897,8983,9045,9109,9172,9240,9347,9441,9537,9629,9727,9823,9879,9936,10102,10187,10264", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,106,104,79,99,97,114,82,65,66,98,67,60,78,87,62,65,63,70,62,73,75,56,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,138,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,93,95,91,97,95,55,56,82,84,76,76", "endOffsets": "320,3172,3277,3357,3457,4286,4401,4484,4550,4617,4716,4784,4845,4924,5012,5075,5141,5205,5276,5339,5413,5489,5546,5600,5709,5768,5831,5885,5959,6084,6174,6252,6341,6424,6504,6649,6732,6814,6953,7044,7127,7179,7232,7298,7369,7449,7520,7600,7678,7756,7829,7904,8011,8098,8185,8276,8369,8441,8517,8609,8660,8742,8808,8892,8978,9040,9104,9167,9235,9342,9436,9532,9624,9722,9818,9874,9931,10014,10182,10259,10336"}}]}]}