{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3300,3396,3498,3597,3696,3800,3903,9973", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3391,3493,3592,3691,3795,3898,4014,10069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,593,691,806,885,945,1010,1100,1167,1226,1303,1393,1457,1521,1584,1653,1717,1781,1849,1905,1961,2073,2131,2193,2249,2321,2443,2530,2605,2696,2777,2858,2998,3075,3156,3283,3374,3451,3505,3556,3622,3692,3769,3840,3915,3986,4063,4132,4201,4308,4399,4471,4560,4649,4723,4795,4881,4931,5010,5076,5156,5240,5302,5366,5429,5498,5598,5686,5778,5863,5954,6042,6100,6155,6233,6314,6389", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,81,77,76,83,97,114,78,59,64,89,66,58,76,89,63,63,62,68,63,63,67,55,55,111,57,61,55,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,87,91,84,90,87,57,54,77,80,74,74", "endOffsets": "267,349,427,504,588,686,801,880,940,1005,1095,1162,1221,1298,1388,1452,1516,1579,1648,1712,1776,1844,1900,1956,2068,2126,2188,2244,2316,2438,2525,2600,2691,2772,2853,2993,3070,3151,3278,3369,3446,3500,3551,3617,3687,3764,3835,3910,3981,4058,4127,4196,4303,4394,4466,4555,4644,4718,4790,4876,4926,5005,5071,5151,5235,5297,5361,5424,5493,5593,5681,5773,5858,5949,6037,6095,6150,6228,6309,6384,6459"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2979,3061,3139,3216,4019,4117,4232,4311,4371,4436,4526,4593,4652,4729,4819,4883,4947,5010,5079,5143,5207,5275,5331,5387,5499,5557,5619,5675,5747,5869,5956,6031,6122,6203,6284,6424,6501,6582,6709,6800,6877,6931,6982,7048,7118,7195,7266,7341,7412,7489,7558,7627,7734,7825,7897,7986,8075,8149,8221,8307,8357,8436,8502,8582,8666,8728,8792,8855,8924,9024,9112,9204,9289,9380,9468,9526,9581,9742,9823,9898", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,81,77,76,83,97,114,78,59,64,89,66,58,76,89,63,63,62,68,63,63,67,55,55,111,57,61,55,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,87,91,84,90,87,57,54,77,80,74,74", "endOffsets": "317,3056,3134,3211,3295,4112,4227,4306,4366,4431,4521,4588,4647,4724,4814,4878,4942,5005,5074,5138,5202,5270,5326,5382,5494,5552,5614,5670,5742,5864,5951,6026,6117,6198,6279,6419,6496,6577,6704,6795,6872,6926,6977,7043,7113,7190,7261,7336,7407,7484,7553,7622,7729,7820,7892,7981,8070,8144,8216,8302,8352,8431,8497,8577,8661,8723,8787,8850,8919,9019,9107,9199,9284,9375,9463,9521,9576,9654,9818,9893,9968"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,9659", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,9737"}}]}]}