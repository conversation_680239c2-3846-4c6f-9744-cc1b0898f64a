{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,8860", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,8934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3127,3219,3320,3414,3508,3601,3695,9157", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3214,3315,3409,3503,3596,3690,3786,9253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,520,611,717,790,841,903,980,1039,1098,1172,1250,1311,1368,1424,1483,1541,1600,1661,1713,1767,1852,1908,1966,2020,2085,2177,2251,2323,2402,2476,2552,2674,2736,2798,2897,2976,3050,3100,3151,3217,3281,3350,3421,3492,3553,3624,3691,3751,3837,3916,3983,4066,4151,4225,4290,4366,4414,4487,4551,4627,4705,4767,4831,4894,4959,5039,5111,5189,5262,5336,5411,5465,5520,5589,5664,5737", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,63,61,69,76,90,105,72,50,61,76,58,58,73,77,60,56,55,58,57,58,60,51,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,71,77,72,73,74,53,54,68,74,72,69", "endOffsets": "242,306,368,438,515,606,712,785,836,898,975,1034,1093,1167,1245,1306,1363,1419,1478,1536,1595,1656,1708,1762,1847,1903,1961,2015,2080,2172,2246,2318,2397,2471,2547,2669,2731,2793,2892,2971,3045,3095,3146,3212,3276,3345,3416,3487,3548,3619,3686,3746,3832,3911,3978,4061,4146,4220,4285,4361,4409,4482,4546,4622,4700,4762,4826,4889,4954,5034,5106,5184,5257,5331,5406,5460,5515,5584,5659,5732,5802"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2854,2918,2980,3050,3791,3882,3988,4061,4112,4174,4251,4310,4369,4443,4521,4582,4639,4695,4754,4812,4871,4932,4984,5038,5123,5179,5237,5291,5356,5448,5522,5594,5673,5747,5823,5945,6007,6069,6168,6247,6321,6371,6422,6488,6552,6621,6692,6763,6824,6895,6962,7022,7108,7187,7254,7337,7422,7496,7561,7637,7685,7758,7822,7898,7976,8038,8102,8165,8230,8310,8382,8460,8533,8607,8682,8736,8791,8939,9014,9087", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,63,61,69,76,90,105,72,50,61,76,58,58,73,77,60,56,55,58,57,58,60,51,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,71,77,72,73,74,53,54,68,74,72,69", "endOffsets": "292,2913,2975,3045,3122,3877,3983,4056,4107,4169,4246,4305,4364,4438,4516,4577,4634,4690,4749,4807,4866,4927,4979,5033,5118,5174,5232,5286,5351,5443,5517,5589,5668,5742,5818,5940,6002,6064,6163,6242,6316,6366,6417,6483,6547,6616,6687,6758,6819,6890,6957,7017,7103,7182,7249,7332,7417,7491,7556,7632,7680,7753,7817,7893,7971,8033,8097,8160,8225,8305,8377,8455,8528,8602,8677,8731,8786,8855,9009,9082,9152"}}]}]}