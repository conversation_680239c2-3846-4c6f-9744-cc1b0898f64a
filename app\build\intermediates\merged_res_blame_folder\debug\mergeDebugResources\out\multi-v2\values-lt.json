{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,705,801,919,1003,1066,1132,1231,1309,1374,1452,1562,1625,1697,1756,1830,1891,1963,2037,2093,2147,2271,2332,2394,2448,2526,2660,2748,2825,2918,2999,3083,3224,3303,3387,3530,3627,3704,3760,3814,3880,3955,4034,4105,4185,4261,4339,4412,4489,4596,4683,4764,4854,4946,5018,5099,5191,5246,5328,5394,5479,5566,5628,5692,5755,5827,5938,6034,6135,6224,6321,6411,6471,6529,6611,6697,6773", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,78,77,82,89,95,117,83,62,65,98,77,64,77,109,62,71,58,73,60,71,73,55,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,95,100,88,96,89,59,57,81,85,75,77", "endOffsets": "370,449,527,610,700,796,914,998,1061,1127,1226,1304,1369,1447,1557,1620,1692,1751,1825,1886,1958,2032,2088,2142,2266,2327,2389,2443,2521,2655,2743,2820,2913,2994,3078,3219,3298,3382,3525,3622,3699,3755,3809,3875,3950,4029,4100,4180,4256,4334,4407,4484,4591,4678,4759,4849,4941,5013,5094,5186,5241,5323,5389,5474,5561,5623,5687,5750,5822,5933,6029,6130,6219,6316,6406,6466,6524,6606,6692,6768,6846"}, "to": {"startLines": "2,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3194,3273,3351,3434,4275,4371,4489,4573,4636,4702,4801,4879,4944,5022,5132,5195,5267,5326,5400,5461,5533,5607,5663,5717,5841,5902,5964,6018,6096,6230,6318,6395,6488,6569,6653,6794,6873,6957,7100,7197,7274,7330,7384,7450,7525,7604,7675,7755,7831,7909,7982,8059,8166,8253,8334,8424,8516,8588,8669,8761,8816,8898,8964,9049,9136,9198,9262,9325,9397,9508,9604,9705,9794,9891,9981,10041,10099,10265,10351,10427", "endLines": "7,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "endColumns": "12,78,77,82,89,95,117,83,62,65,98,77,64,77,109,62,71,58,73,60,71,73,55,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,95,100,88,96,89,59,57,81,85,75,77", "endOffsets": "420,3268,3346,3429,3519,4366,4484,4568,4631,4697,4796,4874,4939,5017,5127,5190,5262,5321,5395,5456,5528,5602,5658,5712,5836,5897,5959,6013,6091,6225,6313,6390,6483,6564,6648,6789,6868,6952,7095,7192,7269,7325,7379,7445,7520,7599,7670,7750,7826,7904,7977,8054,8161,8248,8329,8419,8511,8583,8664,8756,8811,8893,8959,9044,9131,9193,9257,9320,9392,9503,9599,9700,9789,9886,9976,10036,10094,10176,10346,10422,10500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "39,40,41,42,43,44,45,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3524,3622,3732,3831,3934,4045,4155,10505", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3617,3727,3826,3929,4040,4150,4270,10601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "425,541,645,758,845,947,1069,1152,1232,1326,1422,1519,1615,1718,1814,1912,2008,2102,2196,2279,2388,2496,2596,2706,2811,2917,3093,10181", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "536,640,753,840,942,1064,1147,1227,1321,1417,1514,1610,1713,1809,1907,2003,2097,2191,2274,2383,2491,2591,2701,2806,2912,3088,3189,10260"}}]}]}