package com.example.th2_1

import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat

class NumberSelectionActivity : AppCompatActivity() {
    
    private lateinit var etNumberInput: EditText
    private lateinit var btnCreate: Button
    private lateinit var llNumberButtons: LinearLayout
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_number_selection)
        
        initViews()
        setupListeners()
        
        // Get the number from intent and display it
        val number = intent.getIntExtra("NUMBER", 0)
        etNumberInput.setText(number.toString())
        
        // Create number buttons
        createNumberButtons(number)
    }
    
    private fun initViews() {
        etNumberInput = findViewById(R.id.etNumberInput)
        btnCreate = findViewById(R.id.btnCreate)
        llNumberButtons = findViewById(R.id.llNumberButtons)
    }
    
    private fun setupListeners() {
        btnCreate.setOnClickListener {
            val inputText = etNumberInput.text.toString().trim()
            
            if (inputText.isEmpty()) {
                Toast.makeText(this, "Vui lòng nhập số", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            try {
                val number = inputText.toInt()
                if (number > 0) {
                    // Clear existing buttons and create new ones
                    llNumberButtons.removeAllViews()
                    createNumberButtons(number)
                } else {
                    Toast.makeText(this, "Vui lòng nhập số dương", Toast.LENGTH_SHORT).show()
                }
            } catch (e: NumberFormatException) {
                Toast.makeText(this, "Dữ liệu bạn nhập không hợp lệ", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun createNumberButtons(count: Int) {
        llNumberButtons.removeAllViews()
        
        for (i in 1..count) {
            val button = Button(this).apply {
                text = i.toString()
                textSize = 18f
                setTextColor(ContextCompat.getColor(this@NumberSelectionActivity, android.R.color.white))
                background = ContextCompat.getDrawable(this@NumberSelectionActivity, R.drawable.number_button_background)
                
                // Set layout parameters
                val layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    120 // 48dp converted to pixels approximately
                ).apply {
                    setMargins(0, 0, 0, 16)
                }
                this.layoutParams = layoutParams
                
                // Set click listener
                setOnClickListener {
                    Toast.makeText(this@NumberSelectionActivity, "Bạn đã chọn số $i", Toast.LENGTH_SHORT).show()
                }
            }
            
            llNumberButtons.addView(button)
        }
    }
}
