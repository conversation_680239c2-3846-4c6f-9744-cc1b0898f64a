<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    tools:context=".MainActivity">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Thực hành 02"
        android:textSize="18sp"
        android:textColor="#000000"
        android:layout_marginTop="60dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <EditText
        android:id="@+id/etNumberInput"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="200dp"
        android:hint="Nhập vào số lượng"
        android:inputType="number"
        android:background="@drawable/edittext_background"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:textSize="16sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnCreate" />

    <Button
        android:id="@+id/btnCreate"
        android:layout_width="wrap_content"
        android:layout_height="48dp"
        android:layout_marginEnd="32dp"
        android:text="Tạo"
        android:textColor="#FFFFFF"
        android:background="@drawable/button_background"
        android:paddingStart="24dp"
        android:paddingEnd="24dp"
        app:layout_constraintTop_toTopOf="@+id/etNumberInput"
        app:layout_constraintBottom_toBottomOf="@+id/etNumberInput"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tvError"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Dữ liệu bạn nhập không hợp lệ"
        android:textColor="#FF0000"
        android:textSize="14sp"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/etNumberInput"
        app:layout_constraintStart_toStartOf="@+id/etNumberInput" />

</androidx.constraintlayout.widget.ConstraintLayout>