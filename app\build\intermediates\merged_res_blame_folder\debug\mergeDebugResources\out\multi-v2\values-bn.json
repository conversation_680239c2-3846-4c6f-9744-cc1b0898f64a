{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,535,641,730,835,956,1039,1121,1212,1305,1399,1493,1593,1686,1781,1875,1966,2057,2143,2253,2357,2460,2568,2676,2781,2946,9915", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "424,530,636,725,830,951,1034,1116,1207,1300,1394,1488,1588,1681,1776,1870,1961,2052,2138,2248,2352,2455,2563,2671,2776,2941,3046,9997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3417,3516,3618,3720,3823,3924,4026,10237", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3511,3613,3715,3818,3919,4021,4141,10333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,637,741,858,939,1000,1066,1157,1223,1284,1367,1457,1521,1588,1649,1718,1780,1844,1911,1965,2019,2126,2185,2246,2300,2374,2494,2579,2669,2775,2865,2949,3084,3155,3225,3351,3438,3521,3579,3635,3701,3774,3854,3925,4007,4076,4152,4232,4301,4410,4505,4588,4678,4773,4847,4921,5014,5068,5153,5220,5306,5391,5453,5517,5580,5646,5748,5838,5931,6016,6111,6204,6266,6326,6406,6489,6568", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,97,92,82,91,103,116,80,60,65,90,65,60,82,89,63,66,60,68,61,63,66,53,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,125,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,89,92,84,94,92,61,59,79,82,78,72", "endOffsets": "266,364,457,540,632,736,853,934,995,1061,1152,1218,1279,1362,1452,1516,1583,1644,1713,1775,1839,1906,1960,2014,2121,2180,2241,2295,2369,2489,2574,2664,2770,2860,2944,3079,3150,3220,3346,3433,3516,3574,3630,3696,3769,3849,3920,4002,4071,4147,4227,4296,4405,4500,4583,4673,4768,4842,4916,5009,5063,5148,5215,5301,5386,5448,5512,5575,5641,5743,5833,5926,6011,6106,6199,6261,6321,6401,6484,6563,6636"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3051,3149,3242,3325,4146,4250,4367,4448,4509,4575,4666,4732,4793,4876,4966,5030,5097,5158,5227,5289,5353,5420,5474,5528,5635,5694,5755,5809,5883,6003,6088,6178,6284,6374,6458,6593,6664,6734,6860,6947,7030,7088,7144,7210,7283,7363,7434,7516,7585,7661,7741,7810,7919,8014,8097,8187,8282,8356,8430,8523,8577,8662,8729,8815,8900,8962,9026,9089,9155,9257,9347,9440,9525,9620,9713,9775,9835,10002,10085,10164", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,97,92,82,91,103,116,80,60,65,90,65,60,82,89,63,66,60,68,61,63,66,53,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,125,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,89,92,84,94,92,61,59,79,82,78,72", "endOffsets": "316,3144,3237,3320,3412,4245,4362,4443,4504,4570,4661,4727,4788,4871,4961,5025,5092,5153,5222,5284,5348,5415,5469,5523,5630,5689,5750,5804,5878,5998,6083,6173,6279,6369,6453,6588,6659,6729,6855,6942,7025,7083,7139,7205,7278,7358,7429,7511,7580,7656,7736,7805,7914,8009,8092,8182,8277,8351,8425,8518,8572,8657,8724,8810,8895,8957,9021,9084,9150,9252,9342,9435,9520,9615,9708,9770,9830,9910,10080,10159,10232"}}]}]}