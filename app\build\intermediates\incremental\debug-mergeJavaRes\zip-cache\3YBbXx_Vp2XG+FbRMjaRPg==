[{"key": "androidx/lifecycle/Observer.class", "name": "androidx/lifecycle/Observer.class", "size": 581, "crc": -1645925231}, {"key": "androidx/lifecycle/LiveData$1.class", "name": "androidx/lifecycle/LiveData$1.class", "size": 967, "crc": 1624229709}, {"key": "androidx/lifecycle/LiveData$AlwaysActiveObserver.class", "name": "androidx/lifecycle/LiveData$AlwaysActiveObserver.class", "size": 1024, "crc": 2056042322}, {"key": "androidx/lifecycle/LiveData$LifecycleBoundObserver.class", "name": "androidx/lifecycle/LiveData$LifecycleBoundObserver.class", "size": 2882, "crc": 499156433}, {"key": "androidx/lifecycle/LiveData$ObserverWrapper.class", "name": "androidx/lifecycle/LiveData$ObserverWrapper.class", "size": 1588, "crc": -1223836256}, {"key": "androidx/lifecycle/LiveData.class", "name": "androidx/lifecycle/LiveData.class", "size": 8921, "crc": 899075882}, {"key": "androidx/lifecycle/MutableLiveData.class", "name": "androidx/lifecycle/MutableLiveData.class", "size": 946, "crc": -710747396}, {"key": "META-INF/androidx.lifecycle_lifecycle-livedata-core.version", "name": "META-INF/androidx.lifecycle_lifecycle-livedata-core.version", "size": 78, "crc": 1772768774}, {"key": "META-INF/lifecycle-livedata-core_release.kotlin_module", "name": "META-INF/lifecycle-livedata-core_release.kotlin_module", "size": 24, "crc": 1613429616}]