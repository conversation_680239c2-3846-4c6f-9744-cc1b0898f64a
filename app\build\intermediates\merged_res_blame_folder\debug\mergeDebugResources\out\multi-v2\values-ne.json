{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2461,2574,2684,2801,2968,10027", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2456,2569,2679,2796,2963,3074,10102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,610,712,822,909,969,1035,1131,1197,1258,1339,1432,1496,1568,1626,1700,1762,1838,1917,1971,2025,2138,2198,2259,2313,2391,2515,2596,2678,2778,2863,2948,3084,3165,3248,3379,3462,3548,3610,3664,3730,3807,3886,3957,4040,4109,4185,4266,4334,4438,4529,4607,4700,4797,4871,4950,5048,5108,5196,5262,5350,5438,5500,5568,5631,5697,5802,5899,5994,6089,6186,6282,6348,6406,6490,6579,6655", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,88,87,81,88,101,109,86,59,65,95,65,60,80,92,63,71,57,73,61,75,78,53,53,112,59,60,53,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,96,94,94,96,95,65,57,83,88,75,72", "endOffsets": "257,346,434,516,605,707,817,904,964,1030,1126,1192,1253,1334,1427,1491,1563,1621,1695,1757,1833,1912,1966,2020,2133,2193,2254,2308,2386,2510,2591,2673,2773,2858,2943,3079,3160,3243,3374,3457,3543,3605,3659,3725,3802,3881,3952,4035,4104,4180,4261,4329,4433,4524,4602,4695,4792,4866,4945,5043,5103,5191,5257,5345,5433,5495,5563,5626,5692,5797,5894,5989,6084,6181,6277,6343,6401,6485,6574,6650,6723"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3079,3168,3256,3338,4147,4249,4359,4446,4506,4572,4668,4734,4795,4876,4969,5033,5105,5163,5237,5299,5375,5454,5508,5562,5675,5735,5796,5850,5928,6052,6133,6215,6315,6400,6485,6621,6702,6785,6916,6999,7085,7147,7201,7267,7344,7423,7494,7577,7646,7722,7803,7871,7975,8066,8144,8237,8334,8408,8487,8585,8645,8733,8799,8887,8975,9037,9105,9168,9234,9339,9436,9531,9626,9723,9819,9885,9943,10107,10196,10272", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,88,87,81,88,101,109,86,59,65,95,65,60,80,92,63,71,57,73,61,75,78,53,53,112,59,60,53,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,96,94,94,96,95,65,57,83,88,75,72", "endOffsets": "307,3163,3251,3333,3422,4244,4354,4441,4501,4567,4663,4729,4790,4871,4964,5028,5100,5158,5232,5294,5370,5449,5503,5557,5670,5730,5791,5845,5923,6047,6128,6210,6310,6395,6480,6616,6697,6780,6911,6994,7080,7142,7196,7262,7339,7418,7489,7572,7641,7717,7798,7866,7970,8061,8139,8232,8329,8403,8482,8580,8640,8728,8794,8882,8970,9032,9100,9163,9229,9334,9431,9526,9621,9718,9814,9880,9938,10022,10191,10267,10340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3427,3530,3633,3735,3841,3939,4039,10345", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3525,3628,3730,3836,3934,4034,4142,10441"}}]}]}