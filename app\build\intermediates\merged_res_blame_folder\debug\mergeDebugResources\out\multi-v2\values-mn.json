{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,529,638,724,830,944,1027,1108,1199,1292,1387,1483,1580,1673,1767,1859,1950,2040,2120,2227,2330,2427,2534,2636,2749,2908,9898", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "424,524,633,719,825,939,1022,1103,1194,1287,1382,1478,1575,1668,1762,1854,1945,2035,2115,2222,2325,2422,2529,2631,2744,2903,3002,9974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3327,3425,3527,3628,3726,3831,3943,10224", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3420,3522,3623,3721,3826,3938,4057,10320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,585,683,802,887,948,1013,1111,1192,1251,1333,1426,1488,1551,1609,1680,1742,1811,1880,1935,1989,2110,2167,2228,2282,2353,2486,2570,2650,2746,2829,2912,3045,3127,3205,3337,3427,3507,3561,3612,3678,3749,3827,3898,3977,4052,4130,4210,4293,4398,4486,4565,4655,4748,4822,4892,4983,5037,5117,5184,5268,5353,5415,5479,5542,5613,5717,5820,5917,6016,6122,6224,6282,6337,6421,6508,6584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,78,76,78,84,97,118,84,60,64,97,80,58,81,92,61,62,57,70,61,68,68,54,53,120,56,60,53,70,132,83,79,95,82,82,132,81,77,131,89,79,53,50,65,70,77,70,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,102,96,98,105,101,57,54,83,86,75,81", "endOffsets": "260,339,416,495,580,678,797,882,943,1008,1106,1187,1246,1328,1421,1483,1546,1604,1675,1737,1806,1875,1930,1984,2105,2162,2223,2277,2348,2481,2565,2645,2741,2824,2907,3040,3122,3200,3332,3422,3502,3556,3607,3673,3744,3822,3893,3972,4047,4125,4205,4288,4393,4481,4560,4650,4743,4817,4887,4978,5032,5112,5179,5263,5348,5410,5474,5537,5608,5712,5815,5912,6011,6117,6219,6277,6332,6416,6503,6579,6661"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3086,3163,3242,4062,4160,4279,4364,4425,4490,4588,4669,4728,4810,4903,4965,5028,5086,5157,5219,5288,5357,5412,5466,5587,5644,5705,5759,5830,5963,6047,6127,6223,6306,6389,6522,6604,6682,6814,6904,6984,7038,7089,7155,7226,7304,7375,7454,7529,7607,7687,7770,7875,7963,8042,8132,8225,8299,8369,8460,8514,8594,8661,8745,8830,8892,8956,9019,9090,9194,9297,9394,9493,9599,9701,9759,9814,9979,10066,10142", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,78,76,78,84,97,118,84,60,64,97,80,58,81,92,61,62,57,70,61,68,68,54,53,120,56,60,53,70,132,83,79,95,82,82,132,81,77,131,89,79,53,50,65,70,77,70,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,102,96,98,105,101,57,54,83,86,75,81", "endOffsets": "310,3081,3158,3237,3322,4155,4274,4359,4420,4485,4583,4664,4723,4805,4898,4960,5023,5081,5152,5214,5283,5352,5407,5461,5582,5639,5700,5754,5825,5958,6042,6122,6218,6301,6384,6517,6599,6677,6809,6899,6979,7033,7084,7150,7221,7299,7370,7449,7524,7602,7682,7765,7870,7958,8037,8127,8220,8294,8364,8455,8509,8589,8656,8740,8825,8887,8951,9014,9085,9189,9292,9389,9488,9594,9696,9754,9809,9893,10061,10137,10219"}}]}]}