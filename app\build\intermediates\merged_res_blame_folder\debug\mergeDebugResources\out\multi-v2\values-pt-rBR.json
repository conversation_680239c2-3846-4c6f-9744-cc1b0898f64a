{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,605,706,826,907,967,1031,1123,1202,1262,1342,1432,1496,1567,1630,1705,1769,1840,1916,1972,2026,2153,2211,2273,2327,2406,2547,2634,2710,2805,2886,2968,3107,3190,3274,3413,3500,3580,3636,3687,3753,3827,3907,3978,4061,4134,4211,4280,4354,4456,4544,4621,4714,4810,4884,4964,5061,5113,5197,5263,5350,5438,5500,5564,5627,5695,5804,5904,6008,6106,6207,6306,6366,6421,6498,6581,6658", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,80,77,83,88,100,119,80,59,63,91,78,59,79,89,63,70,62,74,63,70,75,55,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,99,103,97,100,98,59,54,76,82,76,78", "endOffsets": "268,349,427,511,600,701,821,902,962,1026,1118,1197,1257,1337,1427,1491,1562,1625,1700,1764,1835,1911,1967,2021,2148,2206,2268,2322,2401,2542,2629,2705,2800,2881,2963,3102,3185,3269,3408,3495,3575,3631,3682,3748,3822,3902,3973,4056,4129,4206,4275,4349,4451,4539,4616,4709,4805,4879,4959,5056,5108,5192,5258,5345,5433,5495,5559,5622,5690,5799,5899,6003,6101,6202,6301,6361,6416,6493,6576,6653,6732"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,4131,4232,4352,4433,4493,4557,4649,4728,4788,4868,4958,5022,5093,5156,5231,5295,5366,5442,5498,5552,5679,5737,5799,5853,5932,6073,6160,6236,6331,6412,6494,6633,6716,6800,6939,7026,7106,7162,7213,7279,7353,7433,7504,7587,7660,7737,7806,7880,7982,8070,8147,8240,8336,8410,8490,8587,8639,8723,8789,8876,8964,9026,9090,9153,9221,9330,9430,9534,9632,9733,9832,9892,9947,10110,10193,10270", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,80,77,83,88,100,119,80,59,63,91,78,59,79,89,63,70,62,74,63,70,75,55,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,99,103,97,100,98,59,54,76,82,76,78", "endOffsets": "318,3137,3215,3299,3388,4227,4347,4428,4488,4552,4644,4723,4783,4863,4953,5017,5088,5151,5226,5290,5361,5437,5493,5547,5674,5732,5794,5848,5927,6068,6155,6231,6326,6407,6489,6628,6711,6795,6934,7021,7101,7157,7208,7274,7348,7428,7499,7582,7655,7732,7801,7875,7977,8065,8142,8235,8331,8405,8485,8582,8634,8718,8784,8871,8959,9021,9085,9148,9216,9325,9425,9529,9627,9728,9827,9887,9942,10019,10188,10265,10344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,10024", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,10105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3393,3490,3592,3691,3791,3901,4011,10349", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "3485,3587,3686,3786,3896,4006,4126,10445"}}]}]}