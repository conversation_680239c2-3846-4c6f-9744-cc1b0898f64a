{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,577,676,796,879,942,1006,1105,1180,1239,1317,1427,1489,1558,1616,1688,1749,1818,1892,1948,2003,2106,2163,2223,2278,2359,2479,2562,2640,2736,2822,2910,3045,3128,3208,3348,3442,3524,3577,3628,3694,3770,3852,3923,4007,4084,4159,4238,4315,4420,4516,4593,4685,4782,4856,4941,5038,5090,5173,5240,5328,5415,5477,5541,5604,5670,5768,5853,5947,6032,6123,6215,6272,6327,6412,6497,6574", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,80,75,76,79,98,119,82,62,63,98,74,58,77,109,61,68,57,71,60,68,73,55,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,84,93,84,90,91,56,54,84,84,76,72", "endOffsets": "258,339,415,492,572,671,791,874,937,1001,1100,1175,1234,1312,1422,1484,1553,1611,1683,1744,1813,1887,1943,1998,2101,2158,2218,2273,2354,2474,2557,2635,2731,2817,2905,3040,3123,3203,3343,3437,3519,3572,3623,3689,3765,3847,3918,4002,4079,4154,4233,4310,4415,4511,4588,4680,4777,4851,4936,5033,5085,5168,5235,5323,5410,5472,5536,5599,5665,5763,5848,5942,6027,6118,6210,6267,6322,6407,6492,6569,6642"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3067,3148,3224,3301,4103,4202,4322,4405,4468,4532,4631,4706,4765,4843,4953,5015,5084,5142,5214,5275,5344,5418,5474,5529,5632,5689,5749,5804,5885,6005,6088,6166,6262,6348,6436,6571,6654,6734,6874,6968,7050,7103,7154,7220,7296,7378,7449,7533,7610,7685,7764,7841,7946,8042,8119,8211,8308,8382,8467,8564,8616,8699,8766,8854,8941,9003,9067,9130,9196,9294,9379,9473,9558,9649,9741,9798,9853,10022,10107,10184", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,80,75,76,79,98,119,82,62,63,98,74,58,77,109,61,68,57,71,60,68,73,55,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,84,93,84,90,91,56,54,84,84,76,72", "endOffsets": "308,3143,3219,3296,3376,4197,4317,4400,4463,4527,4626,4701,4760,4838,4948,5010,5079,5137,5209,5270,5339,5413,5469,5524,5627,5684,5744,5799,5880,6000,6083,6161,6257,6343,6431,6566,6649,6729,6869,6963,7045,7098,7149,7215,7291,7373,7444,7528,7605,7680,7759,7836,7941,8037,8114,8206,8303,8377,8462,8559,8611,8694,8761,8849,8936,8998,9062,9125,9191,9289,9374,9468,9553,9644,9736,9793,9848,9933,10102,10179,10252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3381,3478,3580,3682,3783,3886,3993,10257", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3473,3575,3677,3778,3881,3988,4098,10353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,9938", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,10017"}}]}]}