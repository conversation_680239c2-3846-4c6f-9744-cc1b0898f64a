{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,426,535,647,732,837,954,1033,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2236,2341,2439,2546,2649,2764,2925,9968", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "421,530,642,727,832,949,1028,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2231,2336,2434,2541,2644,2759,2920,3022,10045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,628,729,870,954,1014,1078,1172,1242,1303,1383,1470,1533,1597,1656,1730,1792,1863,1936,1992,2046,2163,2221,2282,2336,2410,2532,2616,2695,2795,2881,2977,3109,3187,3265,3394,3483,3563,3624,3679,3745,3814,3891,3962,4043,4117,4193,4283,4356,4458,4543,4622,4712,4804,4878,4963,5053,5105,5189,5254,5339,5424,5486,5550,5613,5682,5799,5893,5993,6083,6178,6271,6336,6395,6477,6563,6639", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,82,84,84,109,100,140,83,59,63,93,69,60,79,86,62,63,58,73,61,70,72,55,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,93,99,89,94,92,64,58,81,85,75,82", "endOffsets": "260,343,428,513,623,724,865,949,1009,1073,1167,1237,1298,1378,1465,1528,1592,1651,1725,1787,1858,1931,1987,2041,2158,2216,2277,2331,2405,2527,2611,2690,2790,2876,2972,3104,3182,3260,3389,3478,3558,3619,3674,3740,3809,3886,3957,4038,4112,4188,4278,4351,4453,4538,4617,4707,4799,4873,4958,5048,5100,5184,5249,5334,5419,5481,5545,5608,5677,5794,5888,5988,6078,6173,6266,6331,6390,6472,6558,6634,6717"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3027,3110,3195,3280,4119,4220,4361,4445,4505,4569,4663,4733,4794,4874,4961,5024,5088,5147,5221,5283,5354,5427,5483,5537,5654,5712,5773,5827,5901,6023,6107,6186,6286,6372,6468,6600,6678,6756,6885,6974,7054,7115,7170,7236,7305,7382,7453,7534,7608,7684,7774,7847,7949,8034,8113,8203,8295,8369,8454,8544,8596,8680,8745,8830,8915,8977,9041,9104,9173,9290,9384,9484,9574,9669,9762,9827,9886,10050,10136,10212", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,82,84,84,109,100,140,83,59,63,93,69,60,79,86,62,63,58,73,61,70,72,55,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,93,99,89,94,92,64,58,81,85,75,82", "endOffsets": "310,3105,3190,3275,3385,4215,4356,4440,4500,4564,4658,4728,4789,4869,4956,5019,5083,5142,5216,5278,5349,5422,5478,5532,5649,5707,5768,5822,5896,6018,6102,6181,6281,6367,6463,6595,6673,6751,6880,6969,7049,7110,7165,7231,7300,7377,7448,7529,7603,7679,7769,7842,7944,8029,8108,8198,8290,8364,8449,8539,8591,8675,8740,8825,8910,8972,9036,9099,9168,9285,9379,9479,9569,9664,9757,9822,9881,9963,10131,10207,10290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3390,3490,3592,3695,3802,3904,4008,10295", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "3485,3587,3690,3797,3899,4003,4114,10391"}}]}]}