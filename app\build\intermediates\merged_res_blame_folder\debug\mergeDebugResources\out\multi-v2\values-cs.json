{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "418,525,627,737,823,928,1045,1123,1199,1290,1383,1478,1572,1666,1759,1854,1951,2042,2133,2217,2321,2433,2532,2638,2749,2851,3014,9947", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "520,622,732,818,923,1040,1118,1194,1285,1378,1473,1567,1661,1754,1849,1946,2037,2128,2212,2316,2428,2527,2633,2744,2846,3009,3107,10025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,693,785,911,992,1053,1118,1217,1293,1354,1432,1521,1585,1652,1706,1774,1834,1902,1974,2032,2086,2203,2263,2325,2379,2451,2573,2657,2736,2830,2913,3005,3142,3220,3302,3429,3517,3597,3651,3702,3768,3840,3917,3988,4069,4141,4218,4292,4363,4468,4556,4627,4720,4815,4889,4963,5059,5111,5194,5261,5347,5435,5497,5561,5624,5692,5802,5898,5997,6089,6186,6280,6338,6393,6472,6555,6630", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,77,77,76,91,91,125,80,60,64,98,75,60,77,88,63,66,53,67,59,67,71,57,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,95,98,91,96,93,57,54,78,82,74,78", "endOffsets": "363,441,519,596,688,780,906,987,1048,1113,1212,1288,1349,1427,1516,1580,1647,1701,1769,1829,1897,1969,2027,2081,2198,2258,2320,2374,2446,2568,2652,2731,2825,2908,3000,3137,3215,3297,3424,3512,3592,3646,3697,3763,3835,3912,3983,4064,4136,4213,4287,4358,4463,4551,4622,4715,4810,4884,4958,5054,5106,5189,5256,5342,5430,5492,5556,5619,5687,5797,5893,5992,6084,6181,6275,6333,6388,6467,6550,6625,6704"}, "to": {"startLines": "2,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3112,3190,3268,3345,4168,4260,4386,4467,4528,4593,4692,4768,4829,4907,4996,5060,5127,5181,5249,5309,5377,5449,5507,5561,5678,5738,5800,5854,5926,6048,6132,6211,6305,6388,6480,6617,6695,6777,6904,6992,7072,7126,7177,7243,7315,7392,7463,7544,7616,7693,7767,7838,7943,8031,8102,8195,8290,8364,8438,8534,8586,8669,8736,8822,8910,8972,9036,9099,9167,9277,9373,9472,9564,9661,9755,9813,9868,10030,10113,10188", "endLines": "7,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122", "endColumns": "12,77,77,76,91,91,125,80,60,64,98,75,60,77,88,63,66,53,67,59,67,71,57,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,95,98,91,96,93,57,54,78,82,74,78", "endOffsets": "413,3185,3263,3340,3432,4255,4381,4462,4523,4588,4687,4763,4824,4902,4991,5055,5122,5176,5244,5304,5372,5444,5502,5556,5673,5733,5795,5849,5921,6043,6127,6206,6300,6383,6475,6612,6690,6772,6899,6987,7067,7121,7172,7238,7310,7387,7458,7539,7611,7688,7762,7833,7938,8026,8097,8190,8285,8359,8433,8529,8581,8664,8731,8817,8905,8967,9031,9094,9162,9272,9368,9467,9559,9656,9750,9808,9863,9942,10108,10183,10262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "39,40,41,42,43,44,45,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3437,3535,3637,3738,3837,3942,4049,10267", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "3530,3632,3733,3832,3937,4044,4163,10363"}}]}]}