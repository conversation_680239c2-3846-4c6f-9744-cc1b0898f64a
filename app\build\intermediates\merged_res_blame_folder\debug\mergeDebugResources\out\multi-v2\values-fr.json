{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3414,3512,3614,3713,3815,3919,4023,10379", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3507,3609,3708,3810,3914,4018,4136,10475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,10047", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,10129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,622,720,850,935,1000,1066,1163,1246,1308,1389,1491,1556,1631,1687,1766,1826,1893,1964,2021,2075,2197,2256,2318,2372,2454,2589,2681,2756,2851,2932,3016,3160,3239,3320,3461,3554,3633,3688,3739,3805,3885,3966,4037,4117,4190,4268,4341,4413,4525,4618,4690,4782,4874,4948,5032,5124,5181,5265,5331,5414,5501,5563,5627,5690,5768,5870,5960,6057,6149,6237,6325,6384,6439,6528,6615,6692", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,79,80,82,94,97,129,84,64,65,96,82,61,80,101,64,74,55,78,59,66,70,56,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,89,96,91,87,87,58,54,88,86,76,80", "endOffsets": "278,358,439,522,617,715,845,930,995,1061,1158,1241,1303,1384,1486,1551,1626,1682,1761,1821,1888,1959,2016,2070,2192,2251,2313,2367,2449,2584,2676,2751,2846,2927,3011,3155,3234,3315,3456,3549,3628,3683,3734,3800,3880,3961,4032,4112,4185,4263,4336,4408,4520,4613,4685,4777,4869,4943,5027,5119,5176,5260,5326,5409,5496,5558,5622,5685,5763,5865,5955,6052,6144,6232,6320,6379,6434,6523,6610,6687,6768"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,4141,4239,4369,4454,4519,4585,4682,4765,4827,4908,5010,5075,5150,5206,5285,5345,5412,5483,5540,5594,5716,5775,5837,5891,5973,6108,6200,6275,6370,6451,6535,6679,6758,6839,6980,7073,7152,7207,7258,7324,7404,7485,7556,7636,7709,7787,7860,7932,8044,8137,8209,8301,8393,8467,8551,8643,8700,8784,8850,8933,9020,9082,9146,9209,9287,9389,9479,9576,9668,9756,9844,9903,9958,10134,10221,10298", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,79,80,82,94,97,129,84,64,65,96,82,61,80,101,64,74,55,78,59,66,70,56,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,89,96,91,87,87,58,54,88,86,76,80", "endOffsets": "328,3150,3231,3314,3409,4234,4364,4449,4514,4580,4677,4760,4822,4903,5005,5070,5145,5201,5280,5340,5407,5478,5535,5589,5711,5770,5832,5886,5968,6103,6195,6270,6365,6446,6530,6674,6753,6834,6975,7068,7147,7202,7253,7319,7399,7480,7551,7631,7704,7782,7855,7927,8039,8132,8204,8296,8388,8462,8546,8638,8695,8779,8845,8928,9015,9077,9141,9204,9282,9384,9474,9571,9663,9751,9839,9898,9953,10042,10216,10293,10374"}}]}]}