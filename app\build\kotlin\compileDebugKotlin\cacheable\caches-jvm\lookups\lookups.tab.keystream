  white android.R.color  Button android.app.Activity  
ContextCompat android.app.Activity  Intent android.app.Activity  LinearLayout android.app.Activity  NumberFormatException android.app.Activity  NumberSelectionActivity android.app.Activity  R android.app.Activity  Toast android.app.Activity  View android.app.Activity  
ViewCompat android.app.Activity  WindowInsetsCompat android.app.Activity  android android.app.Activity  apply android.app.Activity  enableEdgeToEdge android.app.Activity  intent android.app.Activity  isEmpty android.app.Activity  java android.app.Activity  onCreate android.app.Activity  
startActivity android.app.Activity  toInt android.app.Activity  trim android.app.Activity  Intent android.content  Button android.content.Context  
ContextCompat android.content.Context  Intent android.content.Context  LinearLayout android.content.Context  NumberFormatException android.content.Context  NumberSelectionActivity android.content.Context  R android.content.Context  Toast android.content.Context  View android.content.Context  
ViewCompat android.content.Context  WindowInsetsCompat android.content.Context  android android.content.Context  apply android.content.Context  enableEdgeToEdge android.content.Context  isEmpty android.content.Context  java android.content.Context  toInt android.content.Context  trim android.content.Context  Button android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  Intent android.content.ContextWrapper  LinearLayout android.content.ContextWrapper  NumberFormatException android.content.ContextWrapper  NumberSelectionActivity android.content.ContextWrapper  R android.content.ContextWrapper  Toast android.content.ContextWrapper  View android.content.ContextWrapper  
ViewCompat android.content.ContextWrapper  WindowInsetsCompat android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  isEmpty android.content.ContextWrapper  java android.content.ContextWrapper  toInt android.content.ContextWrapper  trim android.content.ContextWrapper  getIntExtra android.content.Intent  putExtra android.content.Intent  Bundle 
android.os  View android.view  Button  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LinearLayout  android.view.ContextThemeWrapper  NumberFormatException  android.view.ContextThemeWrapper  NumberSelectionActivity  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  
ViewCompat  android.view.ContextThemeWrapper  WindowInsetsCompat  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  isEmpty  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  toInt  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  GONE android.view.View  OnClickListener android.view.View  VISIBLE android.view.View  
background android.view.View  layoutParams android.view.View  setOnClickListener android.view.View  
setPadding android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  addView android.view.ViewGroup  removeAllViews android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  
setMargins )android.view.ViewGroup.MarginLayoutParams  Button android.widget  EditText android.widget  LinearLayout android.widget  TextView android.widget  Toast android.widget  
ContextCompat android.widget.Button  LinearLayout android.widget.Button  R android.widget.Button  Toast android.widget.Button  android android.widget.Button  apply android.widget.Button  
background android.widget.Button  layoutParams android.widget.Button  setOnClickListener android.widget.Button  setTextColor android.widget.Button  text android.widget.Button  textSize android.widget.Button  setText android.widget.EditText  text android.widget.EditText  LayoutParams android.widget.LinearLayout  addView android.widget.LinearLayout  removeAllViews android.widget.LinearLayout  MATCH_PARENT (android.widget.LinearLayout.LayoutParams  apply (android.widget.LinearLayout.LayoutParams  
setMargins (android.widget.LinearLayout.LayoutParams  setText android.widget.TextView  setTextColor android.widget.TextView  text android.widget.TextView  textSize android.widget.TextView  
visibility android.widget.TextView  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  EditText #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LinearLayout #androidx.activity.ComponentActivity  NumberFormatException #androidx.activity.ComponentActivity  NumberSelectionActivity #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  
ViewCompat #androidx.activity.ComponentActivity  WindowInsetsCompat #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  isEmpty #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  toInt #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  Button -androidx.activity.ComponentActivity.Companion  
ContextCompat -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  LinearLayout -androidx.activity.ComponentActivity.Companion  NumberSelectionActivity -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  View -androidx.activity.ComponentActivity.Companion  
ViewCompat -androidx.activity.ComponentActivity.Companion  WindowInsetsCompat -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  isEmpty -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  toInt -androidx.activity.ComponentActivity.Companion  trim -androidx.activity.ComponentActivity.Companion  AppCompatActivity androidx.appcompat.app  Button (androidx.appcompat.app.AppCompatActivity  
ContextCompat (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  LinearLayout (androidx.appcompat.app.AppCompatActivity  NumberFormatException (androidx.appcompat.app.AppCompatActivity  NumberSelectionActivity (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  
ViewCompat (androidx.appcompat.app.AppCompatActivity  WindowInsetsCompat (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  enableEdgeToEdge (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  isEmpty (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  toInt (androidx.appcompat.app.AppCompatActivity  trim (androidx.appcompat.app.AppCompatActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  EditText #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LinearLayout #androidx.core.app.ComponentActivity  NumberFormatException #androidx.core.app.ComponentActivity  NumberSelectionActivity #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  
ViewCompat #androidx.core.app.ComponentActivity  WindowInsetsCompat #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  isEmpty #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  toInt #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  getColor #androidx.core.content.ContextCompat  getDrawable #androidx.core.content.ContextCompat  bottom androidx.core.graphics.Insets  left androidx.core.graphics.Insets  right androidx.core.graphics.Insets  top androidx.core.graphics.Insets  OnApplyWindowInsetsListener androidx.core.view  
ViewCompat androidx.core.view  WindowInsetsCompat androidx.core.view  <SAM-CONSTRUCTOR> .androidx.core.view.OnApplyWindowInsetsListener  setOnApplyWindowInsetsListener androidx.core.view.ViewCompat  	getInsets %androidx.core.view.WindowInsetsCompat  
systemBars *androidx.core.view.WindowInsetsCompat.Type  Button &androidx.fragment.app.FragmentActivity  
ContextCompat &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  LinearLayout &androidx.fragment.app.FragmentActivity  NumberFormatException &androidx.fragment.app.FragmentActivity  NumberSelectionActivity &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  
ViewCompat &androidx.fragment.app.FragmentActivity  WindowInsetsCompat &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  enableEdgeToEdge &androidx.fragment.app.FragmentActivity  isEmpty &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  toInt &androidx.fragment.app.FragmentActivity  trim &androidx.fragment.app.FragmentActivity  AppCompatActivity com.example.th2_1  Bundle com.example.th2_1  Button com.example.th2_1  
ContextCompat com.example.th2_1  EditText com.example.th2_1  Int com.example.th2_1  Intent com.example.th2_1  LinearLayout com.example.th2_1  MainActivity com.example.th2_1  NumberFormatException com.example.th2_1  NumberSelectionActivity com.example.th2_1  R com.example.th2_1  TextView com.example.th2_1  Toast com.example.th2_1  View com.example.th2_1  
ViewCompat com.example.th2_1  WindowInsetsCompat com.example.th2_1  android com.example.th2_1  apply com.example.th2_1  isEmpty com.example.th2_1  java com.example.th2_1  toInt com.example.th2_1  trim com.example.th2_1  Intent com.example.th2_1.MainActivity  NumberSelectionActivity com.example.th2_1.MainActivity  R com.example.th2_1.MainActivity  View com.example.th2_1.MainActivity  
ViewCompat com.example.th2_1.MainActivity  WindowInsetsCompat com.example.th2_1.MainActivity  	btnCreate com.example.th2_1.MainActivity  enableEdgeToEdge com.example.th2_1.MainActivity  
etNumberInput com.example.th2_1.MainActivity  findViewById com.example.th2_1.MainActivity  	initViews com.example.th2_1.MainActivity  isEmpty com.example.th2_1.MainActivity  java com.example.th2_1.MainActivity  setContentView com.example.th2_1.MainActivity  setupListeners com.example.th2_1.MainActivity  	showError com.example.th2_1.MainActivity  
startActivity com.example.th2_1.MainActivity  toInt com.example.th2_1.MainActivity  trim com.example.th2_1.MainActivity  tvError com.example.th2_1.MainActivity  Button )com.example.th2_1.NumberSelectionActivity  
ContextCompat )com.example.th2_1.NumberSelectionActivity  LinearLayout )com.example.th2_1.NumberSelectionActivity  R )com.example.th2_1.NumberSelectionActivity  Toast )com.example.th2_1.NumberSelectionActivity  android )com.example.th2_1.NumberSelectionActivity  apply )com.example.th2_1.NumberSelectionActivity  	btnCreate )com.example.th2_1.NumberSelectionActivity  createNumberButtons )com.example.th2_1.NumberSelectionActivity  
etNumberInput )com.example.th2_1.NumberSelectionActivity  findViewById )com.example.th2_1.NumberSelectionActivity  	initViews )com.example.th2_1.NumberSelectionActivity  intent )com.example.th2_1.NumberSelectionActivity  isEmpty )com.example.th2_1.NumberSelectionActivity  llNumberButtons )com.example.th2_1.NumberSelectionActivity  setContentView )com.example.th2_1.NumberSelectionActivity  setupListeners )com.example.th2_1.NumberSelectionActivity  toInt )com.example.th2_1.NumberSelectionActivity  trim )com.example.th2_1.NumberSelectionActivity  number_button_background com.example.th2_1.R.drawable  	btnCreate com.example.th2_1.R.id  
etNumberInput com.example.th2_1.R.id  llNumberButtons com.example.th2_1.R.id  main com.example.th2_1.R.id  tvError com.example.th2_1.R.id  
activity_main com.example.th2_1.R.layout  activity_number_selection com.example.th2_1.R.layout  Class 	java.lang  NumberFormatException 	java.lang  CharSequence kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  apply kotlin  toString 
kotlin.Any  isEmpty kotlin.CharSequence  	compareTo 
kotlin.Int  rangeTo 
kotlin.Int  toString 
kotlin.Int  isEmpty 
kotlin.String  toInt 
kotlin.String  trim 
kotlin.String  IntIterator kotlin.collections  isEmpty kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  java 
kotlin.jvm  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  java kotlin.reflect.KClass  isEmpty kotlin.text  toInt kotlin.text  trim kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         