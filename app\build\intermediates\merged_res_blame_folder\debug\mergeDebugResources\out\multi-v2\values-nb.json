{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,578,677,796,878,937,1001,1093,1161,1221,1301,1388,1452,1514,1578,1646,1711,1777,1845,1901,1955,2064,2122,2184,2238,2313,2433,2515,2592,2682,2766,2846,2980,3058,3138,3261,3349,3427,3481,3532,3598,3666,3740,3811,3887,3958,4036,4106,4176,4276,4365,4443,4531,4619,4691,4763,4849,4900,4978,5044,5125,5208,5270,5334,5397,5466,5566,5655,5748,5833,5925,6013,6071,6126,6204,6288,6366", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,76,72,86,79,98,118,81,58,63,91,67,59,79,86,63,61,63,67,64,65,67,55,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,87,71,71,85,50,77,65,80,82,61,63,62,68,99,88,92,84,91,87,57,54,77,83,77,71", "endOffsets": "256,333,406,493,573,672,791,873,932,996,1088,1156,1216,1296,1383,1447,1509,1573,1641,1706,1772,1840,1896,1950,2059,2117,2179,2233,2308,2428,2510,2587,2677,2761,2841,2975,3053,3133,3256,3344,3422,3476,3527,3593,3661,3735,3806,3882,3953,4031,4101,4171,4271,4360,4438,4526,4614,4686,4758,4844,4895,4973,5039,5120,5203,5265,5329,5392,5461,5561,5650,5743,5828,5920,6008,6066,6121,6199,6283,6361,6433"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3986,4085,4204,4286,4345,4409,4501,4569,4629,4709,4796,4860,4922,4986,5054,5119,5185,5253,5309,5363,5472,5530,5592,5646,5721,5841,5923,6000,6090,6174,6254,6388,6466,6546,6669,6757,6835,6889,6940,7006,7074,7148,7219,7295,7366,7444,7514,7584,7684,7773,7851,7939,8027,8099,8171,8257,8308,8386,8452,8533,8616,8678,8742,8805,8874,8974,9063,9156,9241,9333,9421,9479,9534,9692,9776,9854", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,76,72,86,79,98,118,81,58,63,91,67,59,79,86,63,61,63,67,64,65,67,55,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,87,71,71,85,50,77,65,80,82,61,63,62,68,99,88,92,84,91,87,57,54,77,83,77,71", "endOffsets": "306,3015,3088,3175,3255,4080,4199,4281,4340,4404,4496,4564,4624,4704,4791,4855,4917,4981,5049,5114,5180,5248,5304,5358,5467,5525,5587,5641,5716,5836,5918,5995,6085,6169,6249,6383,6461,6541,6664,6752,6830,6884,6935,7001,7069,7143,7214,7290,7361,7439,7509,7579,7679,7768,7846,7934,8022,8094,8166,8252,8303,8381,8447,8528,8611,8673,8737,8800,8869,8969,9058,9151,9236,9328,9416,9474,9529,9607,9771,9849,9921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3260,3354,3456,3553,3652,3760,3866,9926", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3349,3451,3548,3647,3755,3861,3981,10022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,9612", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,9687"}}]}]}