{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3410,3507,3609,3710,3807,3914,4022,10458", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3502,3604,3705,3802,3909,4017,4139,10554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "330,441,549,662,750,856,971,1051,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2368,2469,2579,2697,2805,2968,10133", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "436,544,657,745,851,966,1046,1123,1214,1307,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2363,2464,2574,2692,2800,2963,3065,10213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,280,364,444,530,620,725,861,946,1006,1071,1170,1238,1297,1377,1466,1534,1601,1664,1739,1807,1875,1947,2003,2057,2177,2235,2297,2351,2426,2568,2658,2736,2830,2913,2998,3143,3227,3310,3456,3552,3629,3687,3738,3804,3878,3956,4027,4113,4187,4266,4339,4411,4527,4631,4704,4803,4903,4977,5052,5159,5211,5300,5367,5458,5552,5614,5678,5741,5811,5930,6024,6133,6222,6317,6407,6469,6524,6609,6696,6774", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,83,79,85,89,104,135,84,59,64,98,67,58,79,88,67,66,62,74,67,67,71,55,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,93,108,88,94,89,61,54,84,86,77,74", "endOffsets": "275,359,439,525,615,720,856,941,1001,1066,1165,1233,1292,1372,1461,1529,1596,1659,1734,1802,1870,1942,1998,2052,2172,2230,2292,2346,2421,2563,2653,2731,2825,2908,2993,3138,3222,3305,3451,3547,3624,3682,3733,3799,3873,3951,4022,4108,4182,4261,4334,4406,4522,4626,4699,4798,4898,4972,5047,5154,5206,5295,5362,5453,5547,5609,5673,5736,5806,5925,6019,6128,6217,6312,6402,6464,6519,6604,6691,6769,6844"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3154,3234,3320,4144,4249,4385,4470,4530,4595,4694,4762,4821,4901,4990,5058,5125,5188,5263,5331,5399,5471,5527,5581,5701,5759,5821,5875,5950,6092,6182,6260,6354,6437,6522,6667,6751,6834,6980,7076,7153,7211,7262,7328,7402,7480,7551,7637,7711,7790,7863,7935,8051,8155,8228,8327,8427,8501,8576,8683,8735,8824,8891,8982,9076,9138,9202,9265,9335,9454,9548,9657,9746,9841,9931,9993,10048,10218,10305,10383", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,83,79,85,89,104,135,84,59,64,98,67,58,79,88,67,66,62,74,67,67,71,55,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,93,108,88,94,89,61,54,84,86,77,74", "endOffsets": "325,3149,3229,3315,3405,4244,4380,4465,4525,4590,4689,4757,4816,4896,4985,5053,5120,5183,5258,5326,5394,5466,5522,5576,5696,5754,5816,5870,5945,6087,6177,6255,6349,6432,6517,6662,6746,6829,6975,7071,7148,7206,7257,7323,7397,7475,7546,7632,7706,7785,7858,7930,8046,8150,8223,8322,8422,8496,8571,8678,8730,8819,8886,8977,9071,9133,9197,9260,9330,9449,9543,9652,9741,9836,9926,9988,10043,10128,10300,10378,10453"}}]}]}