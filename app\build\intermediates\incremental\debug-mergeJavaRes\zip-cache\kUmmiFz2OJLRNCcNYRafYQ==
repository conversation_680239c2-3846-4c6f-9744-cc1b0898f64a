[{"key": "com/google/android/material/animation/AnimatableView$Listener.class", "name": "com/google/android/material/animation/AnimatableView$Listener.class", "size": 287, "crc": -1857674807}, {"key": "com/google/android/material/animation/AnimatableView.class", "name": "com/google/android/material/animation/AnimatableView.class", "size": 464, "crc": -682140970}, {"key": "com/google/android/material/animation/AnimationUtils.class", "name": "com/google/android/material/animation/AnimationUtils.class", "size": 1858, "crc": 533063927}, {"key": "com/google/android/material/animation/AnimatorSetCompat.class", "name": "com/google/android/material/animation/AnimatorSetCompat.class", "size": 1893, "crc": -1567523501}, {"key": "com/google/android/material/animation/ArgbEvaluatorCompat.class", "name": "com/google/android/material/animation/ArgbEvaluatorCompat.class", "size": 2064, "crc": 1935238374}, {"key": "com/google/android/material/animation/ChildrenAlphaProperty.class", "name": "com/google/android/material/animation/ChildrenAlphaProperty.class", "size": 2269, "crc": -717300373}, {"key": "com/google/android/material/animation/DrawableAlphaProperty.class", "name": "com/google/android/material/animation/DrawableAlphaProperty.class", "size": 1754, "crc": -1979792170}, {"key": "com/google/android/material/animation/ImageMatrixProperty.class", "name": "com/google/android/material/animation/ImageMatrixProperty.class", "size": 1614, "crc": -1708801589}, {"key": "com/google/android/material/animation/MatrixEvaluator.class", "name": "com/google/android/material/animation/MatrixEvaluator.class", "size": 1485, "crc": -588802014}, {"key": "com/google/android/material/animation/MotionSpec.class", "name": "com/google/android/material/animation/MotionSpec.class", "size": 7905, "crc": 1231584030}, {"key": "com/google/android/material/animation/MotionTiming.class", "name": "com/google/android/material/animation/MotionTiming.class", "size": 4109, "crc": -1357414921}, {"key": "com/google/android/material/animation/Positioning.class", "name": "com/google/android/material/animation/Positioning.class", "size": 504, "crc": 2001597003}, {"key": "com/google/android/material/animation/TransformationCallback.class", "name": "com/google/android/material/animation/TransformationCallback.class", "size": 333, "crc": -1514163484}, {"key": "com/google/android/material/appbar/AppBarLayout$1.class", "name": "com/google/android/material/appbar/AppBarLayout$1.class", "size": 1146, "crc": 82612727}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$1.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$1.class", "size": 1936, "crc": 1682026987}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$2.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$2.class", "size": 3855, "crc": -1287415814}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$BaseDragCallback.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$BaseDragCallback.class", "size": 981, "crc": -1016773581}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$SavedState$1.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$SavedState$1.class", "size": 2306, "crc": -1277983488}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$SavedState.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior$SavedState.class", "size": 2367, "crc": 544080291}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseBehavior.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseBehavior.class", "size": 26374, "crc": 1602102919}, {"key": "com/google/android/material/appbar/AppBarLayout$BaseOnOffsetChangedListener.class", "name": "com/google/android/material/appbar/AppBarLayout$BaseOnOffsetChangedListener.class", "size": 476, "crc": -902577768}, {"key": "com/google/android/material/appbar/AppBarLayout$Behavior$DragCallback.class", "name": "com/google/android/material/appbar/AppBarLayout$Behavior$DragCallback.class", "size": 885, "crc": -1316284633}, {"key": "com/google/android/material/appbar/AppBarLayout$Behavior.class", "name": "com/google/android/material/appbar/AppBarLayout$Behavior.class", "size": 4856, "crc": 1860433796}, {"key": "com/google/android/material/appbar/AppBarLayout$ChildScrollEffect.class", "name": "com/google/android/material/appbar/AppBarLayout$ChildScrollEffect.class", "size": 664, "crc": -1182227770}, {"key": "com/google/android/material/appbar/AppBarLayout$CompressChildScrollEffect.class", "name": "com/google/android/material/appbar/AppBarLayout$CompressChildScrollEffect.class", "size": 2282, "crc": 1187556528}, {"key": "com/google/android/material/appbar/AppBarLayout$LayoutParams$ScrollEffect.class", "name": "com/google/android/material/appbar/AppBarLayout$LayoutParams$ScrollEffect.class", "size": 771, "crc": -94203378}, {"key": "com/google/android/material/appbar/AppBarLayout$LayoutParams$ScrollFlags.class", "name": "com/google/android/material/appbar/AppBarLayout$LayoutParams$ScrollFlags.class", "size": 769, "crc": 70354373}, {"key": "com/google/android/material/appbar/AppBarLayout$LayoutParams.class", "name": "com/google/android/material/appbar/AppBarLayout$LayoutParams.class", "size": 5446, "crc": 938529111}, {"key": "com/google/android/material/appbar/AppBarLayout$LiftOnScrollListener.class", "name": "com/google/android/material/appbar/AppBarLayout$LiftOnScrollListener.class", "size": 504, "crc": 1551425134}, {"key": "com/google/android/material/appbar/AppBarLayout$LiftOnScrollProgressListener.class", "name": "com/google/android/material/appbar/AppBarLayout$LiftOnScrollProgressListener.class", "size": 738, "crc": -1129236104}, {"key": "com/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener.class", "name": "com/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener.class", "size": 646, "crc": -625020822}, {"key": "com/google/android/material/appbar/AppBarLayout$ScrollingViewBehavior.class", "name": "com/google/android/material/appbar/AppBarLayout$ScrollingViewBehavior.class", "size": 7912, "crc": -826608463}, {"key": "com/google/android/material/appbar/AppBarLayout.class", "name": "com/google/android/material/appbar/AppBarLayout.class", "size": 33614, "crc": -2055992718}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$1.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$1.class", "size": 1298, "crc": 1706521253}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$2.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$2.class", "size": 1257, "crc": 1121729702}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$CollapsedTitleGravityMode.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$CollapsedTitleGravityMode.class", "size": 728, "crc": -1040378640}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$LayoutParams.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$LayoutParams.class", "size": 3415, "crc": -781456662}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$OffsetUpdateListener.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$OffsetUpdateListener.class", "size": 3553, "crc": 1789540236}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$StaticLayoutBuilderConfigurer.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$StaticLayoutBuilderConfigurer.class", "size": 689, "crc": 865182883}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout$TitleCollapseMode.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout$TitleCollapseMode.class", "size": 712, "crc": 420929083}, {"key": "com/google/android/material/appbar/CollapsingToolbarLayout.class", "name": "com/google/android/material/appbar/CollapsingToolbarLayout.class", "size": 42008, "crc": -2110536293}, {"key": "com/google/android/material/appbar/HeaderBehavior$FlingRunnable.class", "name": "com/google/android/material/appbar/HeaderBehavior$FlingRunnable.class", "size": 1803, "crc": -1734571987}, {"key": "com/google/android/material/appbar/HeaderBehavior.class", "name": "com/google/android/material/appbar/HeaderBehavior.class", "size": 7263, "crc": -869640289}, {"key": "com/google/android/material/appbar/HeaderScrollingViewBehavior.class", "name": "com/google/android/material/appbar/HeaderScrollingViewBehavior.class", "size": 5746, "crc": -481685308}, {"key": "com/google/android/material/appbar/MaterialToolbar.class", "name": "com/google/android/material/appbar/MaterialToolbar.class", "size": 10491, "crc": 257709627}, {"key": "com/google/android/material/appbar/ViewOffsetBehavior.class", "name": "com/google/android/material/appbar/ViewOffsetBehavior.class", "size": 3616, "crc": -1409007410}, {"key": "com/google/android/material/appbar/ViewOffsetHelper.class", "name": "com/google/android/material/appbar/ViewOffsetHelper.class", "size": 2141, "crc": -1184469891}, {"key": "com/google/android/material/appbar/ViewUtilsLollipop.class", "name": "com/google/android/material/appbar/ViewUtilsLollipop.class", "size": 3201, "crc": 1562872382}, {"key": "com/google/android/material/badge/BadgeDrawable$BadgeGravity.class", "name": "com/google/android/material/badge/BadgeDrawable$BadgeGravity.class", "size": 432, "crc": -2040590062}, {"key": "com/google/android/material/badge/BadgeDrawable.class", "name": "com/google/android/material/badge/BadgeDrawable.class", "size": 28662, "crc": 1799974345}, {"key": "com/google/android/material/badge/BadgeState$State$1.class", "name": "com/google/android/material/badge/BadgeState$State$1.class", "size": 1539, "crc": -262385829}, {"key": "com/google/android/material/badge/BadgeState$State.class", "name": "com/google/android/material/badge/BadgeState$State.class", "size": 10683, "crc": -434404165}, {"key": "com/google/android/material/badge/BadgeState.class", "name": "com/google/android/material/badge/BadgeState.class", "size": 20042, "crc": 934474127}, {"key": "com/google/android/material/badge/BadgeUtils$1.class", "name": "com/google/android/material/badge/BadgeUtils$1.class", "size": 1940, "crc": 1753701780}, {"key": "com/google/android/material/badge/BadgeUtils$2.class", "name": "com/google/android/material/badge/BadgeUtils$2.class", "size": 1626, "crc": -1804772566}, {"key": "com/google/android/material/badge/BadgeUtils$3.class", "name": "com/google/android/material/badge/BadgeUtils$3.class", "size": 1370, "crc": -1425391874}, {"key": "com/google/android/material/badge/BadgeUtils$4.class", "name": "com/google/android/material/badge/BadgeUtils$4.class", "size": 1372, "crc": 806820022}, {"key": "com/google/android/material/badge/BadgeUtils.class", "name": "com/google/android/material/badge/BadgeUtils.class", "size": 9317, "crc": -598167011}, {"key": "com/google/android/material/badge/ExperimentalBadgeUtils.class", "name": "com/google/android/material/badge/ExperimentalBadgeUtils.class", "size": 806, "crc": -1964326152}, {"key": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$1.class", "name": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$1.class", "size": 2331, "crc": 742784068}, {"key": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$2.class", "name": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$2.class", "size": 1256, "crc": -847976994}, {"key": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$OnScrollStateChangedListener.class", "name": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$OnScrollStateChangedListener.class", "size": 479, "crc": -1608301979}, {"key": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$ScrollState.class", "name": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior$ScrollState.class", "size": 725, "crc": 1203834126}, {"key": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior.class", "name": "com/google/android/material/behavior/HideBottomViewOnScrollBehavior.class", "size": 12606, "crc": -110648227}, {"key": "com/google/android/material/behavior/HideBottomViewOnScrollDelegate.class", "name": "com/google/android/material/behavior/HideBottomViewOnScrollDelegate.class", "size": 2332, "crc": -712603181}, {"key": "com/google/android/material/behavior/HideLeftViewOnScrollDelegate.class", "name": "com/google/android/material/behavior/HideLeftViewOnScrollDelegate.class", "size": 2325, "crc": -68305988}, {"key": "com/google/android/material/behavior/HideRightViewOnScrollDelegate.class", "name": "com/google/android/material/behavior/HideRightViewOnScrollDelegate.class", "size": 2327, "crc": 139933725}, {"key": "com/google/android/material/behavior/HideViewOnScrollBehavior$1.class", "name": "com/google/android/material/behavior/HideViewOnScrollBehavior$1.class", "size": 2277, "crc": -1248207444}, {"key": "com/google/android/material/behavior/HideViewOnScrollBehavior$2.class", "name": "com/google/android/material/behavior/HideViewOnScrollBehavior$2.class", "size": 1214, "crc": -513216575}, {"key": "com/google/android/material/behavior/HideViewOnScrollBehavior$OnScrollStateChangedListener.class", "name": "com/google/android/material/behavior/HideViewOnScrollBehavior$OnScrollStateChangedListener.class", "size": 461, "crc": -326752747}, {"key": "com/google/android/material/behavior/HideViewOnScrollBehavior$ScrollState.class", "name": "com/google/android/material/behavior/HideViewOnScrollBehavior$ScrollState.class", "size": 707, "crc": 1520782509}, {"key": "com/google/android/material/behavior/HideViewOnScrollBehavior.class", "name": "com/google/android/material/behavior/HideViewOnScrollBehavior.class", "size": 15675, "crc": 792489235}, {"key": "com/google/android/material/behavior/HideViewOnScrollDelegate.class", "name": "com/google/android/material/behavior/HideViewOnScrollDelegate.class", "size": 1248, "crc": 1668240541}, {"key": "com/google/android/material/behavior/SwipeDismissBehavior$1.class", "name": "com/google/android/material/behavior/SwipeDismissBehavior$1.class", "size": 5427, "crc": -548847371}, {"key": "com/google/android/material/behavior/SwipeDismissBehavior$2.class", "name": "com/google/android/material/behavior/SwipeDismissBehavior$2.class", "size": 2196, "crc": 1951972197}, {"key": "com/google/android/material/behavior/SwipeDismissBehavior$OnDismissListener.class", "name": "com/google/android/material/behavior/SwipeDismissBehavior$OnDismissListener.class", "size": 371, "crc": 304515231}, {"key": "com/google/android/material/behavior/SwipeDismissBehavior$SettleRunnable.class", "name": "com/google/android/material/behavior/SwipeDismissBehavior$SettleRunnable.class", "size": 1670, "crc": 1961865513}, {"key": "com/google/android/material/behavior/SwipeDismissBehavior.class", "name": "com/google/android/material/behavior/SwipeDismissBehavior.class", "size": 7649, "crc": 741553675}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$1.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$1.class", "size": 1185, "crc": -1586106679}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$2.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$2.class", "size": 2876, "crc": 961510660}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$3.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$3.class", "size": 2473, "crc": -1602773495}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$4.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$4.class", "size": 1188, "crc": -1947725637}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$5$1.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$5$1.class", "size": 1353, "crc": -1177152411}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$5.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$5.class", "size": 1697, "crc": 1253982338}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$6.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$6.class", "size": 1285, "crc": 1430901305}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$7.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$7.class", "size": 1640, "crc": 97681557}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$8.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$8.class", "size": 1300, "crc": -1483215448}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$9.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$9.class", "size": 1562, "crc": -799824293}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$AnimationListener.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$AnimationListener.class", "size": 387, "crc": 476601032}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$Behavior$1.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$Behavior$1.class", "size": 4306, "crc": 1059768223}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$Behavior.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$Behavior.class", "size": 5742, "crc": -1167467172}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$FabAlignmentMode.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$FabAlignmentMode.class", "size": 451, "crc": 2009858017}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$FabAnchorMode.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$FabAnchorMode.class", "size": 683, "crc": -182953427}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$FabAnimationMode.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$FabAnimationMode.class", "size": 451, "crc": 354709216}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$MenuAlignmentMode.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$MenuAlignmentMode.class", "size": 691, "crc": 1738643298}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$SavedState$1.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$SavedState$1.class", "size": 2170, "crc": -82198511}, {"key": "com/google/android/material/bottomappbar/BottomAppBar$SavedState.class", "name": "com/google/android/material/bottomappbar/BottomAppBar$SavedState.class", "size": 1786, "crc": 2058606482}, {"key": "com/google/android/material/bottomappbar/BottomAppBar.class", "name": "com/google/android/material/bottomappbar/BottomAppBar.class", "size": 34647, "crc": -1912435105}, {"key": "com/google/android/material/bottomappbar/BottomAppBarTopEdgeTreatment.class", "name": "com/google/android/material/bottomappbar/BottomAppBarTopEdgeTreatment.class", "size": 4441, "crc": -497812951}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationItemView.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationItemView.class", "size": 1369, "crc": 2059520801}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationMenuView.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationMenuView.class", "size": 6389, "crc": 639688843}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationView$1.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationView$1.class", "size": 2251, "crc": -1948609778}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationView$OnNavigationItemReselectedListener.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationView$OnNavigationItemReselectedListener.class", "size": 610, "crc": 1430928807}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationView$OnNavigationItemSelectedListener.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationView$OnNavigationItemSelectedListener.class", "size": 602, "crc": -1991714807}, {"key": "com/google/android/material/bottomnavigation/BottomNavigationView.class", "name": "com/google/android/material/bottomnavigation/BottomNavigationView.class", "size": 7146, "crc": 268235656}, {"key": "com/google/android/material/bottomnavigation/LabelVisibilityMode.class", "name": "com/google/android/material/bottomnavigation/LabelVisibilityMode.class", "size": 674, "crc": 1046625177}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$1.class", "size": 1146, "crc": 2017071922}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$2.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$2.class", "size": 1258, "crc": 1590165071}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$3.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$3.class", "size": 1615, "crc": -525377116}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$4.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$4.class", "size": 3917, "crc": 1844122677}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$5.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$5.class", "size": 5218, "crc": -662350366}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$6.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$6.class", "size": 1576, "crc": -881218990}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$BottomSheetCallback.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$BottomSheetCallback.class", "size": 868, "crc": 1942453929}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$SaveFlags.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$SaveFlags.class", "size": 694, "crc": -358030929}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$SavedState$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$SavedState$1.class", "size": 2225, "crc": -1032902390}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$SavedState.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$SavedState.class", "size": 3130, "crc": 2074908506}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$StableState.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$StableState.class", "size": 698, "crc": 1921869501}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$State.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$State.class", "size": 686, "crc": 944087389}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$StateSettlingTracker$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$StateSettlingTracker$1.class", "size": 1735, "crc": -53095803}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior$StateSettlingTracker.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior$StateSettlingTracker.class", "size": 2381, "crc": 1985449041}, {"key": "com/google/android/material/bottomsheet/BottomSheetBehavior.class", "name": "com/google/android/material/bottomsheet/BottomSheetBehavior.class", "size": 51341, "crc": 132927862}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$1.class", "size": 2708, "crc": 117972521}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$2.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$2.class", "size": 1288, "crc": -1971041434}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$3.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$3.class", "size": 1833, "crc": 1088206344}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$4.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$4.class", "size": 1174, "crc": -1927972044}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$5.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$5.class", "size": 1306, "crc": 2115526344}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog$EdgeToEdgeCallback.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog$EdgeToEdgeCallback.class", "size": 4513, "crc": 1288719442}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialog.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialog.class", "size": 12024, "crc": -1358809363}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialogFragment$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialogFragment$1.class", "size": 303, "crc": -809346315}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialogFragment$BottomSheetDismissCallback.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialogFragment$BottomSheetDismissCallback.class", "size": 1796, "crc": -1532366971}, {"key": "com/google/android/material/bottomsheet/BottomSheetDialogFragment.class", "name": "com/google/android/material/bottomsheet/BottomSheetDialogFragment.class", "size": 3705, "crc": 1538354522}, {"key": "com/google/android/material/bottomsheet/BottomSheetDragHandleView$1.class", "name": "com/google/android/material/bottomsheet/BottomSheetDragHandleView$1.class", "size": 1399, "crc": 575049178}, {"key": "com/google/android/material/bottomsheet/BottomSheetDragHandleView$2.class", "name": "com/google/android/material/bottomsheet/BottomSheetDragHandleView$2.class", "size": 1755, "crc": 811364069}, {"key": "com/google/android/material/bottomsheet/BottomSheetDragHandleView$3.class", "name": "com/google/android/material/bottomsheet/BottomSheetDragHandleView$3.class", "size": 1423, "crc": 144133010}, {"key": "com/google/android/material/bottomsheet/BottomSheetDragHandleView.class", "name": "com/google/android/material/bottomsheet/BottomSheetDragHandleView.class", "size": 11030, "crc": -1907376458}, {"key": "com/google/android/material/bottomsheet/InsetsAnimationCallback.class", "name": "com/google/android/material/bottomsheet/InsetsAnimationCallback.class", "size": 3015, "crc": -1508184280}, {"key": "com/google/android/material/button/MaterialButton$1.class", "name": "com/google/android/material/button/MaterialButton$1.class", "size": 1344, "crc": -1677950888}, {"key": "com/google/android/material/button/MaterialButton$IconGravity.class", "name": "com/google/android/material/button/MaterialButton$IconGravity.class", "size": 435, "crc": 1114225902}, {"key": "com/google/android/material/button/MaterialButton$InspectionCompanion.class", "name": "com/google/android/material/button/MaterialButton$InspectionCompanion.class", "size": 2378, "crc": -1008089549}, {"key": "com/google/android/material/button/MaterialButton$OnCheckedChangeListener.class", "name": "com/google/android/material/button/MaterialButton$OnCheckedChangeListener.class", "size": 365, "crc": -457600563}, {"key": "com/google/android/material/button/MaterialButton$OnPressedChangeListener.class", "name": "com/google/android/material/button/MaterialButton$OnPressedChangeListener.class", "size": 365, "crc": 954939044}, {"key": "com/google/android/material/button/MaterialButton$SavedState$1.class", "name": "com/google/android/material/button/MaterialButton$SavedState$1.class", "size": 2107, "crc": 139703252}, {"key": "com/google/android/material/button/MaterialButton$SavedState.class", "name": "com/google/android/material/button/MaterialButton$SavedState.class", "size": 2021, "crc": -1646896273}, {"key": "com/google/android/material/button/MaterialButton.class", "name": "com/google/android/material/button/MaterialButton.class", "size": 35913, "crc": 1559197816}, {"key": "com/google/android/material/button/MaterialButtonGroup$1.class", "name": "com/google/android/material/button/MaterialButtonGroup$1.class", "size": 275, "crc": -1353624146}, {"key": "com/google/android/material/button/MaterialButtonGroup$PressedStateTracker.class", "name": "com/google/android/material/button/MaterialButtonGroup$PressedStateTracker.class", "size": 1564, "crc": -755581452}, {"key": "com/google/android/material/button/MaterialButtonGroup.class", "name": "com/google/android/material/button/MaterialButtonGroup.class", "size": 19876, "crc": 861301272}, {"key": "com/google/android/material/button/MaterialButtonHelper.class", "name": "com/google/android/material/button/MaterialButtonHelper.class", "size": 14638, "crc": -1524953578}, {"key": "com/google/android/material/button/MaterialButtonToggleGroup$1.class", "name": "com/google/android/material/button/MaterialButtonToggleGroup$1.class", "size": 1917, "crc": -1341127533}, {"key": "com/google/android/material/button/MaterialButtonToggleGroup$OnButtonCheckedListener.class", "name": "com/google/android/material/button/MaterialButtonToggleGroup$OnButtonCheckedListener.class", "size": 495, "crc": 496882846}, {"key": "com/google/android/material/button/MaterialButtonToggleGroup.class", "name": "com/google/android/material/button/MaterialButtonToggleGroup.class", "size": 12548, "crc": 426326471}, {"key": "com/google/android/material/button/MaterialSplitButton.class", "name": "com/google/android/material/button/MaterialSplitButton.class", "size": 4451, "crc": -253143817}, {"key": "com/google/android/material/canvas/CanvasCompat$CanvasOperation.class", "name": "com/google/android/material/canvas/CanvasCompat$CanvasOperation.class", "size": 387, "crc": -1314111425}, {"key": "com/google/android/material/canvas/CanvasCompat.class", "name": "com/google/android/material/canvas/CanvasCompat.class", "size": 1666, "crc": 91531798}, {"key": "com/google/android/material/card/MaterialCardView$CheckedIconGravity.class", "name": "com/google/android/material/card/MaterialCardView$CheckedIconGravity.class", "size": 451, "crc": 1096419111}, {"key": "com/google/android/material/card/MaterialCardView$OnCheckedChangeListener.class", "name": "com/google/android/material/card/MaterialCardView$OnCheckedChangeListener.class", "size": 367, "crc": -499151301}, {"key": "com/google/android/material/card/MaterialCardView.class", "name": "com/google/android/material/card/MaterialCardView.class", "size": 15413, "crc": -2111524232}, {"key": "com/google/android/material/card/MaterialCardViewHelper$1.class", "name": "com/google/android/material/card/MaterialCardViewHelper$1.class", "size": 1364, "crc": -1391763131}, {"key": "com/google/android/material/card/MaterialCardViewHelper.class", "name": "com/google/android/material/card/MaterialCardViewHelper.class", "size": 21236, "crc": 945544767}, {"key": "com/google/android/material/carousel/Arrangement.class", "name": "com/google/android/material/carousel/Arrangement.class", "size": 4311, "crc": 1927936059}, {"key": "com/google/android/material/carousel/Carousel.class", "name": "com/google/android/material/carousel/Carousel.class", "size": 549, "crc": 1456854499}, {"key": "com/google/android/material/carousel/CarouselLayoutManager$1.class", "name": "com/google/android/material/carousel/CarouselLayoutManager$1.class", "size": 2006, "crc": -247653629}, {"key": "com/google/android/material/carousel/CarouselLayoutManager$ChildCalculations.class", "name": "com/google/android/material/carousel/CarouselLayoutManager$ChildCalculations.class", "size": 1008, "crc": -1448195783}, {"key": "com/google/android/material/carousel/CarouselLayoutManager$DebugItemDecoration.class", "name": "com/google/android/material/carousel/CarouselLayoutManager$DebugItemDecoration.class", "size": 3433, "crc": 1781815519}, {"key": "com/google/android/material/carousel/CarouselLayoutManager$KeylineRange.class", "name": "com/google/android/material/carousel/CarouselLayoutManager$KeylineRange.class", "size": 1083, "crc": -159805801}, {"key": "com/google/android/material/carousel/CarouselLayoutManager$LayoutDirection.class", "name": "com/google/android/material/carousel/CarouselLayoutManager$LayoutDirection.class", "size": 658, "crc": 1225190098}, {"key": "com/google/android/material/carousel/CarouselLayoutManager.class", "name": "com/google/android/material/carousel/CarouselLayoutManager.class", "size": 37285, "crc": -1994114015}, {"key": "com/google/android/material/carousel/CarouselOrientationHelper$1.class", "name": "com/google/android/material/carousel/CarouselOrientationHelper$1.class", "size": 4215, "crc": 216098655}, {"key": "com/google/android/material/carousel/CarouselOrientationHelper$2.class", "name": "com/google/android/material/carousel/CarouselOrientationHelper$2.class", "size": 4384, "crc": 666734738}, {"key": "com/google/android/material/carousel/CarouselOrientationHelper.class", "name": "com/google/android/material/carousel/CarouselOrientationHelper.class", "size": 2627, "crc": -1453498095}, {"key": "com/google/android/material/carousel/CarouselSnapHelper$1.class", "name": "com/google/android/material/carousel/CarouselSnapHelper$1.class", "size": 3090, "crc": -1922391894}, {"key": "com/google/android/material/carousel/CarouselSnapHelper.class", "name": "com/google/android/material/carousel/CarouselSnapHelper.class", "size": 6379, "crc": 1751186692}, {"key": "com/google/android/material/carousel/CarouselStrategy$StrategyType.class", "name": "com/google/android/material/carousel/CarouselStrategy$StrategyType.class", "size": 1439, "crc": -1997971933}, {"key": "com/google/android/material/carousel/CarouselStrategy.class", "name": "com/google/android/material/carousel/CarouselStrategy.class", "size": 2881, "crc": 521800184}, {"key": "com/google/android/material/carousel/CarouselStrategyHelper.class", "name": "com/google/android/material/carousel/CarouselStrategyHelper.class", "size": 5794, "crc": 1821420163}, {"key": "com/google/android/material/carousel/FullScreenCarouselStrategy.class", "name": "com/google/android/material/carousel/FullScreenCarouselStrategy.class", "size": 2327, "crc": -629813185}, {"key": "com/google/android/material/carousel/HeroCarouselStrategy.class", "name": "com/google/android/material/carousel/HeroCarouselStrategy.class", "size": 4434, "crc": 2047146702}, {"key": "com/google/android/material/carousel/KeylineState$1.class", "name": "com/google/android/material/carousel/KeylineState$1.class", "size": 258, "crc": 1001323491}, {"key": "com/google/android/material/carousel/KeylineState$Builder.class", "name": "com/google/android/material/carousel/KeylineState$Builder.class", "size": 6022, "crc": -1912464529}, {"key": "com/google/android/material/carousel/KeylineState$Keyline.class", "name": "com/google/android/material/carousel/KeylineState$Keyline.class", "size": 1593, "crc": -142165544}, {"key": "com/google/android/material/carousel/KeylineState.class", "name": "com/google/android/material/carousel/KeylineState.class", "size": 6317, "crc": 1583103082}, {"key": "com/google/android/material/carousel/KeylineStateList$1.class", "name": "com/google/android/material/carousel/KeylineStateList$1.class", "size": 942, "crc": 296982192}, {"key": "com/google/android/material/carousel/KeylineStateList.class", "name": "com/google/android/material/carousel/KeylineStateList.class", "size": 17372, "crc": -939236474}, {"key": "com/google/android/material/carousel/Maskable.class", "name": "com/google/android/material/carousel/Maskable.class", "size": 795, "crc": -1974660222}, {"key": "com/google/android/material/carousel/MaskableFrameLayout.class", "name": "com/google/android/material/carousel/MaskableFrameLayout.class", "size": 9965, "crc": 1496644942}, {"key": "com/google/android/material/carousel/MultiBrowseCarouselStrategy.class", "name": "com/google/android/material/carousel/MultiBrowseCarouselStrategy.class", "size": 4666, "crc": -805715330}, {"key": "com/google/android/material/carousel/OnMaskChangedListener.class", "name": "com/google/android/material/carousel/OnMaskChangedListener.class", "size": 298, "crc": 1970668846}, {"key": "com/google/android/material/carousel/UncontainedCarouselStrategy.class", "name": "com/google/android/material/carousel/UncontainedCarouselStrategy.class", "size": 5651, "crc": -178923055}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$1.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$1.class", "size": 1634, "crc": -1084105876}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$CheckedState.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$CheckedState.class", "size": 685, "crc": 188596570}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$OnCheckedStateChangedListener.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$OnCheckedStateChangedListener.class", "size": 490, "crc": -2030738174}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$OnErrorChangedListener.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$OnErrorChangedListener.class", "size": 461, "crc": -136371521}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$SavedState$1.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$SavedState$1.class", "size": 1595, "crc": 1810904135}, {"key": "com/google/android/material/checkbox/MaterialCheckBox$SavedState.class", "name": "com/google/android/material/checkbox/MaterialCheckBox$SavedState.class", "size": 2838, "crc": -911256767}, {"key": "com/google/android/material/checkbox/MaterialCheckBox.class", "name": "com/google/android/material/checkbox/MaterialCheckBox.class", "size": 20643, "crc": 825427336}, {"key": "com/google/android/material/chip/Chip$1.class", "name": "com/google/android/material/chip/Chip$1.class", "size": 1539, "crc": 789122626}, {"key": "com/google/android/material/chip/Chip$2.class", "name": "com/google/android/material/chip/Chip$2.class", "size": 1238, "crc": -1026028389}, {"key": "com/google/android/material/chip/Chip$ChipTouchHelper.class", "name": "com/google/android/material/chip/Chip$ChipTouchHelper.class", "size": 5455, "crc": 684036536}, {"key": "com/google/android/material/chip/Chip.class", "name": "com/google/android/material/chip/Chip.class", "size": 45371, "crc": -799397636}, {"key": "com/google/android/material/chip/ChipDrawable$Delegate.class", "name": "com/google/android/material/chip/ChipDrawable$Delegate.class", "size": 281, "crc": -893404587}, {"key": "com/google/android/material/chip/ChipDrawable.class", "name": "com/google/android/material/chip/ChipDrawable.class", "size": 48266, "crc": 1245234412}, {"key": "com/google/android/material/chip/ChipGroup$1.class", "name": "com/google/android/material/chip/ChipGroup$1.class", "size": 1753, "crc": 314632388}, {"key": "com/google/android/material/chip/ChipGroup$2.class", "name": "com/google/android/material/chip/ChipGroup$2.class", "size": 1983, "crc": -162712716}, {"key": "com/google/android/material/chip/ChipGroup$LayoutParams.class", "name": "com/google/android/material/chip/ChipGroup$LayoutParams.class", "size": 1208, "crc": -1261466465}, {"key": "com/google/android/material/chip/ChipGroup$OnCheckedChangeListener.class", "name": "com/google/android/material/chip/ChipGroup$OnCheckedChangeListener.class", "size": 543, "crc": -1161141749}, {"key": "com/google/android/material/chip/ChipGroup$OnCheckedStateChangeListener.class", "name": "com/google/android/material/chip/ChipGroup$OnCheckedStateChangeListener.class", "size": 561, "crc": -990546787}, {"key": "com/google/android/material/chip/ChipGroup$PassThroughHierarchyChangeListener.class", "name": "com/google/android/material/chip/ChipGroup$PassThroughHierarchyChangeListener.class", "size": 2415, "crc": -1104358100}, {"key": "com/google/android/material/chip/ChipGroup.class", "name": "com/google/android/material/chip/ChipGroup.class", "size": 12723, "crc": -820281317}, {"key": "com/google/android/material/circularreveal/CircularRevealCompat$1.class", "name": "com/google/android/material/circularreveal/CircularRevealCompat$1.class", "size": 1378, "crc": -1968843686}, {"key": "com/google/android/material/circularreveal/CircularRevealCompat.class", "name": "com/google/android/material/circularreveal/CircularRevealCompat.class", "size": 3376, "crc": 846060923}, {"key": "com/google/android/material/circularreveal/CircularRevealFrameLayout.class", "name": "com/google/android/material/circularreveal/CircularRevealFrameLayout.class", "size": 3387, "crc": 1483948558}, {"key": "com/google/android/material/circularreveal/CircularRevealGridLayout.class", "name": "com/google/android/material/circularreveal/CircularRevealGridLayout.class", "size": 3268, "crc": 1274083532}, {"key": "com/google/android/material/circularreveal/CircularRevealHelper$Delegate.class", "name": "com/google/android/material/circularreveal/CircularRevealHelper$Delegate.class", "size": 367, "crc": 336240421}, {"key": "com/google/android/material/circularreveal/CircularRevealHelper$Strategy.class", "name": "com/google/android/material/circularreveal/CircularRevealHelper$Strategy.class", "size": 463, "crc": 278256303}, {"key": "com/google/android/material/circularreveal/CircularRevealHelper.class", "name": "com/google/android/material/circularreveal/CircularRevealHelper.class", "size": 6643, "crc": 1854244288}, {"key": "com/google/android/material/circularreveal/CircularRevealLinearLayout.class", "name": "com/google/android/material/circularreveal/CircularRevealLinearLayout.class", "size": 3276, "crc": -827874740}, {"key": "com/google/android/material/circularreveal/CircularRevealRelativeLayout.class", "name": "com/google/android/material/circularreveal/CircularRevealRelativeLayout.class", "size": 3284, "crc": 2049827421}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget$1.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget$1.class", "size": 294, "crc": -505616497}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealEvaluator.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealEvaluator.class", "size": 2325, "crc": 1595941663}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealProperty.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealProperty.class", "size": 2458, "crc": 1067672229}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealScrimColorProperty.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget$CircularRevealScrimColorProperty.class", "size": 2121, "crc": 577672180}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget$RevealInfo.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget$RevealInfo.class", "size": 1720, "crc": -145719658}, {"key": "com/google/android/material/circularreveal/CircularRevealWidget.class", "name": "com/google/android/material/circularreveal/CircularRevealWidget.class", "size": 1760, "crc": 1966706979}, {"key": "com/google/android/material/circularreveal/cardview/CircularRevealCardView.class", "name": "com/google/android/material/circularreveal/cardview/CircularRevealCardView.class", "size": 3291, "crc": -1187104182}, {"key": "com/google/android/material/circularreveal/coordinatorlayout/CircularRevealCoordinatorLayout.class", "name": "com/google/android/material/circularreveal/coordinatorlayout/CircularRevealCoordinatorLayout.class", "size": 3366, "crc": 1734160734}, {"key": "com/google/android/material/color/ColorContrast$ColorContrastActivityLifecycleCallbacks$1.class", "name": "com/google/android/material/color/ColorContrast$ColorContrastActivityLifecycleCallbacks$1.class", "size": 1710, "crc": 1420477544}, {"key": "com/google/android/material/color/ColorContrast$ColorContrastActivityLifecycleCallbacks.class", "name": "com/google/android/material/color/ColorContrast$ColorContrastActivityLifecycleCallbacks.class", "size": 3958, "crc": 681302026}, {"key": "com/google/android/material/color/ColorContrast.class", "name": "com/google/android/material/color/ColorContrast.class", "size": 3395, "crc": 815349002}, {"key": "com/google/android/material/color/ColorContrastOptions$1.class", "name": "com/google/android/material/color/ColorContrastOptions$1.class", "size": 276, "crc": 186861772}, {"key": "com/google/android/material/color/ColorContrastOptions$Builder.class", "name": "com/google/android/material/color/ColorContrastOptions$Builder.class", "size": 1725, "crc": 1346747836}, {"key": "com/google/android/material/color/ColorContrastOptions.class", "name": "com/google/android/material/color/ColorContrastOptions.class", "size": 1541, "crc": 1815214737}, {"key": "com/google/android/material/color/ColorResourcesLoaderCreator.class", "name": "com/google/android/material/color/ColorResourcesLoaderCreator.class", "size": 3655, "crc": -1716187958}, {"key": "com/google/android/material/color/ColorResourcesOverride.class", "name": "com/google/android/material/color/ColorResourcesOverride.class", "size": 1449, "crc": 819230862}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$1.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$1.class", "size": 1380, "crc": -1377907257}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$ColorResource.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$ColorResource.class", "size": 1612, "crc": -1219231992}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$PackageChunk.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$PackageChunk.class", "size": 4657, "crc": 1969104597}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$PackageInfo.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$PackageInfo.class", "size": 978, "crc": 2080073301}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$ResChunkHeader.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$ResChunkHeader.class", "size": 1058, "crc": -15586806}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$ResEntry.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$ResEntry.class", "size": 1312, "crc": 1938849853}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$ResTable.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$ResTable.class", "size": 4098, "crc": -310734014}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$StringPoolChunk.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$StringPoolChunk.class", "size": 5404, "crc": -1342696879}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$StringStyledSpan.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$StringStyledSpan.class", "size": 1188, "crc": 1074722685}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$TypeChunk.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$TypeChunk.class", "size": 3508, "crc": -960267508}, {"key": "com/google/android/material/color/ColorResourcesTableCreator$TypeSpecChunk.class", "name": "com/google/android/material/color/ColorResourcesTableCreator$TypeSpecChunk.class", "size": 3272, "crc": -460135353}, {"key": "com/google/android/material/color/ColorResourcesTableCreator.class", "name": "com/google/android/material/color/ColorResourcesTableCreator.class", "size": 8165, "crc": -1059612065}, {"key": "com/google/android/material/color/ColorRoles.class", "name": "com/google/android/material/color/ColorRoles.class", "size": 1049, "crc": -1173578338}, {"key": "com/google/android/material/color/DynamicColors$1.class", "name": "com/google/android/material/color/DynamicColors$1.class", "size": 637, "crc": -929332584}, {"key": "com/google/android/material/color/DynamicColors$2.class", "name": "com/google/android/material/color/DynamicColors$2.class", "size": 1404, "crc": 747672846}, {"key": "com/google/android/material/color/DynamicColors$DeviceSupportCondition.class", "name": "com/google/android/material/color/DynamicColors$DeviceSupportCondition.class", "size": 301, "crc": 504624192}, {"key": "com/google/android/material/color/DynamicColors$DynamicColorsActivityLifecycleCallbacks.class", "name": "com/google/android/material/color/DynamicColors$DynamicColorsActivityLifecycleCallbacks.class", "size": 2136, "crc": 1833179761}, {"key": "com/google/android/material/color/DynamicColors$OnAppliedCallback.class", "name": "com/google/android/material/color/DynamicColors$OnAppliedCallback.class", "size": 395, "crc": -1812336391}, {"key": "com/google/android/material/color/DynamicColors$Precondition.class", "name": "com/google/android/material/color/DynamicColors$Precondition.class", "size": 440, "crc": -1712063868}, {"key": "com/google/android/material/color/DynamicColors.class", "name": "com/google/android/material/color/DynamicColors.class", "size": 10790, "crc": 211020055}, {"key": "com/google/android/material/color/DynamicColorsOptions$1.class", "name": "com/google/android/material/color/DynamicColorsOptions$1.class", "size": 888, "crc": -2111220757}, {"key": "com/google/android/material/color/DynamicColorsOptions$2.class", "name": "com/google/android/material/color/DynamicColorsOptions$2.class", "size": 857, "crc": 1560648729}, {"key": "com/google/android/material/color/DynamicColorsOptions$Builder.class", "name": "com/google/android/material/color/DynamicColorsOptions$Builder.class", "size": 4022, "crc": -1932068845}, {"key": "com/google/android/material/color/DynamicColorsOptions.class", "name": "com/google/android/material/color/DynamicColorsOptions.class", "size": 4120, "crc": -1370002300}, {"key": "com/google/android/material/color/HarmonizedColorAttributes.class", "name": "com/google/android/material/color/HarmonizedColorAttributes.class", "size": 2157, "crc": -2060482975}, {"key": "com/google/android/material/color/HarmonizedColors.class", "name": "com/google/android/material/color/HarmonizedColors.class", "size": 5676, "crc": -2068959317}, {"key": "com/google/android/material/color/HarmonizedColorsOptions$1.class", "name": "com/google/android/material/color/HarmonizedColorsOptions$1.class", "size": 285, "crc": -17188425}, {"key": "com/google/android/material/color/HarmonizedColorsOptions$Builder.class", "name": "com/google/android/material/color/HarmonizedColorsOptions$Builder.class", "size": 2649, "crc": -333410284}, {"key": "com/google/android/material/color/HarmonizedColorsOptions.class", "name": "com/google/android/material/color/HarmonizedColorsOptions.class", "size": 2921, "crc": -1757935427}, {"key": "com/google/android/material/color/MaterialColorUtilitiesHelper.class", "name": "com/google/android/material/color/MaterialColorUtilitiesHelper.class", "size": 7698, "crc": 461704924}, {"key": "com/google/android/material/color/MaterialColors.class", "name": "com/google/android/material/color/MaterialColors.class", "size": 8425, "crc": -185067715}, {"key": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride$1.class", "name": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride$1.class", "size": 327, "crc": -1896575467}, {"key": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride$ResourcesLoaderColorResourcesOverrideSingleton.class", "name": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride$ResourcesLoaderColorResourcesOverrideSingleton.class", "size": 1132, "crc": 1876706295}, {"key": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride.class", "name": "com/google/android/material/color/ResourcesLoaderColorResourcesOverride.class", "size": 2854, "crc": -1978770777}, {"key": "com/google/android/material/color/ResourcesLoaderUtils.class", "name": "com/google/android/material/color/ResourcesLoaderUtils.class", "size": 1604, "crc": -1988065610}, {"key": "com/google/android/material/color/ThemeUtils.class", "name": "com/google/android/material/color/ThemeUtils.class", "size": 1899, "crc": 181763829}, {"key": "com/google/android/material/color/utilities/Blend.class", "name": "com/google/android/material/color/utilities/Blend.class", "size": 2593, "crc": 1103753518}, {"key": "com/google/android/material/color/utilities/Cam16.class", "name": "com/google/android/material/color/utilities/Cam16.class", "size": 9010, "crc": 1794989730}, {"key": "com/google/android/material/color/utilities/ColorUtils.class", "name": "com/google/android/material/color/utilities/ColorUtils.class", "size": 5068, "crc": -1509164858}, {"key": "com/google/android/material/color/utilities/Contrast.class", "name": "com/google/android/material/color/utilities/Contrast.class", "size": 2434, "crc": -1009592929}, {"key": "com/google/android/material/color/utilities/ContrastCurve.class", "name": "com/google/android/material/color/utilities/ContrastCurve.class", "size": 1175, "crc": -2071535795}, {"key": "com/google/android/material/color/utilities/CorePalette.class", "name": "com/google/android/material/color/utilities/CorePalette.class", "size": 2001, "crc": -934301319}, {"key": "com/google/android/material/color/utilities/DislikeAnalyzer.class", "name": "com/google/android/material/color/utilities/DislikeAnalyzer.class", "size": 1607, "crc": 830037211}, {"key": "com/google/android/material/color/utilities/DynamicColor.class", "name": "com/google/android/material/color/utilities/DynamicColor.class", "size": 13979, "crc": 1930343316}, {"key": "com/google/android/material/color/utilities/DynamicScheme.class", "name": "com/google/android/material/color/utilities/DynamicScheme.class", "size": 11060, "crc": -229831577}, {"key": "com/google/android/material/color/utilities/Hct.class", "name": "com/google/android/material/color/utilities/Hct.class", "size": 3040, "crc": 659484022}, {"key": "com/google/android/material/color/utilities/HctSolver.class", "name": "com/google/android/material/color/utilities/HctSolver.class", "size": 12823, "crc": **********}, {"key": "com/google/android/material/color/utilities/MaterialDynamicColors.class", "name": "com/google/android/material/color/utilities/MaterialDynamicColors.class", "size": 38163, "crc": **********}, {"key": "com/google/android/material/color/utilities/MathUtils.class", "name": "com/google/android/material/color/utilities/MathUtils.class", "size": 2083, "crc": -876780226}, {"key": "com/google/android/material/color/utilities/PointProvider.class", "name": "com/google/android/material/color/utilities/PointProvider.class", "size": 499, "crc": 619185834}, {"key": "com/google/android/material/color/utilities/PointProviderLab.class", "name": "com/google/android/material/color/utilities/PointProviderLab.class", "size": 1266, "crc": **********}, {"key": "com/google/android/material/color/utilities/Quantizer.class", "name": "com/google/android/material/color/utilities/Quantizer.class", "size": 500, "crc": -**********}, {"key": "com/google/android/material/color/utilities/QuantizerCelebi.class", "name": "com/google/android/material/color/utilities/QuantizerCelebi.class", "size": 2030, "crc": -486017678}, {"key": "com/google/android/material/color/utilities/QuantizerMap.class", "name": "com/google/android/material/color/utilities/QuantizerMap.class", "size": 1909, "crc": **********}, {"key": "com/google/android/material/color/utilities/QuantizerResult.class", "name": "com/google/android/material/color/utilities/QuantizerResult.class", "size": 912, "crc": 723918283}, {"key": "com/google/android/material/color/utilities/QuantizerWsmeans$Distance.class", "name": "com/google/android/material/color/utilities/QuantizerWsmeans$Distance.class", "size": 1157, "crc": **********}, {"key": "com/google/android/material/color/utilities/QuantizerWsmeans.class", "name": "com/google/android/material/color/utilities/QuantizerWsmeans.class", "size": 5536, "crc": 1094744072}, {"key": "com/google/android/material/color/utilities/QuantizerWu$1.class", "name": "com/google/android/material/color/utilities/QuantizerWu$1.class", "size": 269, "crc": -1592572263}, {"key": "com/google/android/material/color/utilities/QuantizerWu$Box.class", "name": "com/google/android/material/color/utilities/QuantizerWu$Box.class", "size": 960, "crc": -442793892}, {"key": "com/google/android/material/color/utilities/QuantizerWu$CreateBoxesResult.class", "name": "com/google/android/material/color/utilities/QuantizerWu$CreateBoxesResult.class", "size": 588, "crc": -1802374759}, {"key": "com/google/android/material/color/utilities/QuantizerWu$Direction.class", "name": "com/google/android/material/color/utilities/QuantizerWu$Direction.class", "size": 1466, "crc": -2112062756}, {"key": "com/google/android/material/color/utilities/QuantizerWu$MaximizeResult.class", "name": "com/google/android/material/color/utilities/QuantizerWu$MaximizeResult.class", "size": 615, "crc": 1883405873}, {"key": "com/google/android/material/color/utilities/QuantizerWu.class", "name": "com/google/android/material/color/utilities/QuantizerWu.class", "size": 11738, "crc": 763921192}, {"key": "com/google/android/material/color/utilities/Scheme.class", "name": "com/google/android/material/color/utilities/Scheme.class", "size": 15679, "crc": 812509419}, {"key": "com/google/android/material/color/utilities/SchemeContent.class", "name": "com/google/android/material/color/utilities/SchemeContent.class", "size": 2443, "crc": 1423364938}, {"key": "com/google/android/material/color/utilities/SchemeExpressive.class", "name": "com/google/android/material/color/utilities/SchemeExpressive.class", "size": 2489, "crc": -676127975}, {"key": "com/google/android/material/color/utilities/SchemeFidelity.class", "name": "com/google/android/material/color/utilities/SchemeFidelity.class", "size": 2393, "crc": 980372009}, {"key": "com/google/android/material/color/utilities/SchemeFruitSalad.class", "name": "com/google/android/material/color/utilities/SchemeFruitSalad.class", "size": 1876, "crc": -790148732}, {"key": "com/google/android/material/color/utilities/SchemeMonochrome.class", "name": "com/google/android/material/color/utilities/SchemeMonochrome.class", "size": 1698, "crc": -760327487}, {"key": "com/google/android/material/color/utilities/SchemeNeutral.class", "name": "com/google/android/material/color/utilities/SchemeNeutral.class", "size": 1732, "crc": -584990756}, {"key": "com/google/android/material/color/utilities/SchemeRainbow.class", "name": "com/google/android/material/color/utilities/SchemeRainbow.class", "size": 1839, "crc": 964725384}, {"key": "com/google/android/material/color/utilities/SchemeTonalSpot.class", "name": "com/google/android/material/color/utilities/SchemeTonalSpot.class", "size": 1870, "crc": -244210137}, {"key": "com/google/android/material/color/utilities/SchemeVibrant.class", "name": "com/google/android/material/color/utilities/SchemeVibrant.class", "size": 2335, "crc": 871850554}, {"key": "com/google/android/material/color/utilities/Score$ScoredComparator.class", "name": "com/google/android/material/color/utilities/Score$ScoredComparator.class", "size": 1231, "crc": -1131005209}, {"key": "com/google/android/material/color/utilities/Score$ScoredHCT.class", "name": "com/google/android/material/color/utilities/Score$ScoredHCT.class", "size": 650, "crc": -1333999505}, {"key": "com/google/android/material/color/utilities/Score.class", "name": "com/google/android/material/color/utilities/Score.class", "size": 5518, "crc": 1790353460}, {"key": "com/google/android/material/color/utilities/TemperatureCache.class", "name": "com/google/android/material/color/utilities/TemperatureCache.class", "size": 8794, "crc": 586360950}, {"key": "com/google/android/material/color/utilities/TonalPalette$KeyColor.class", "name": "com/google/android/material/color/utilities/TonalPalette$KeyColor.class", "size": 2192, "crc": -924720280}, {"key": "com/google/android/material/color/utilities/TonalPalette.class", "name": "com/google/android/material/color/utilities/TonalPalette.class", "size": 2849, "crc": -1297065300}, {"key": "com/google/android/material/color/utilities/ToneDeltaPair.class", "name": "com/google/android/material/color/utilities/ToneDeltaPair.class", "size": 1818, "crc": -416164188}, {"key": "com/google/android/material/color/utilities/TonePolarity.class", "name": "com/google/android/material/color/utilities/TonePolarity.class", "size": 1639, "crc": 936831223}, {"key": "com/google/android/material/color/utilities/Variant.class", "name": "com/google/android/material/color/utilities/Variant.class", "size": 1897, "crc": -1798788200}, {"key": "com/google/android/material/color/utilities/ViewingConditions.class", "name": "com/google/android/material/color/utilities/ViewingConditions.class", "size": 4076, "crc": -1267702067}, {"key": "com/google/android/material/datepicker/CalendarConstraints$1.class", "name": "com/google/android/material/datepicker/CalendarConstraints$1.class", "size": 2461, "crc": 891627724}, {"key": "com/google/android/material/datepicker/CalendarConstraints$Builder.class", "name": "com/google/android/material/datepicker/CalendarConstraints$Builder.class", "size": 4540, "crc": -1403433194}, {"key": "com/google/android/material/datepicker/CalendarConstraints$DateValidator.class", "name": "com/google/android/material/datepicker/CalendarConstraints$DateValidator.class", "size": 337, "crc": 1338631109}, {"key": "com/google/android/material/datepicker/CalendarConstraints.class", "name": "com/google/android/material/datepicker/CalendarConstraints.class", "size": 6730, "crc": 2032109123}, {"key": "com/google/android/material/datepicker/CalendarItemStyle.class", "name": "com/google/android/material/datepicker/CalendarItemStyle.class", "size": 5902, "crc": 1901590375}, {"key": "com/google/android/material/datepicker/CalendarStyle.class", "name": "com/google/android/material/datepicker/CalendarStyle.class", "size": 2972, "crc": 2116693713}, {"key": "com/google/android/material/datepicker/CompositeDateValidator$1.class", "name": "com/google/android/material/datepicker/CompositeDateValidator$1.class", "size": 1723, "crc": 1195482659}, {"key": "com/google/android/material/datepicker/CompositeDateValidator$2.class", "name": "com/google/android/material/datepicker/CompositeDateValidator$2.class", "size": 1723, "crc": 1315390679}, {"key": "com/google/android/material/datepicker/CompositeDateValidator$3.class", "name": "com/google/android/material/datepicker/CompositeDateValidator$3.class", "size": 2895, "crc": 334936329}, {"key": "com/google/android/material/datepicker/CompositeDateValidator$Operator.class", "name": "com/google/android/material/datepicker/CompositeDateValidator$Operator.class", "size": 716, "crc": 40775167}, {"key": "com/google/android/material/datepicker/CompositeDateValidator.class", "name": "com/google/android/material/datepicker/CompositeDateValidator.class", "size": 4138, "crc": 1064537998}, {"key": "com/google/android/material/datepicker/DateFormatTextWatcher.class", "name": "com/google/android/material/datepicker/DateFormatTextWatcher.class", "size": 6653, "crc": -1928311909}, {"key": "com/google/android/material/datepicker/DateSelector.class", "name": "com/google/android/material/datepicker/DateSelector.class", "size": 4590, "crc": -1890045831}, {"key": "com/google/android/material/datepicker/DateStrings.class", "name": "com/google/android/material/datepicker/DateStrings.class", "size": 6053, "crc": 1974247790}, {"key": "com/google/android/material/datepicker/DateValidatorPointBackward$1.class", "name": "com/google/android/material/datepicker/DateValidatorPointBackward$1.class", "size": 1688, "crc": -541423497}, {"key": "com/google/android/material/datepicker/DateValidatorPointBackward.class", "name": "com/google/android/material/datepicker/DateValidatorPointBackward.class", "size": 2788, "crc": 1979258568}, {"key": "com/google/android/material/datepicker/DateValidatorPointForward$1.class", "name": "com/google/android/material/datepicker/DateValidatorPointForward$1.class", "size": 1680, "crc": -1163004952}, {"key": "com/google/android/material/datepicker/DateValidatorPointForward.class", "name": "com/google/android/material/datepicker/DateValidatorPointForward.class", "size": 2777, "crc": -983769288}, {"key": "com/google/android/material/datepicker/DayViewDecorator.class", "name": "com/google/android/material/datepicker/DayViewDecorator.class", "size": 2259, "crc": -1737250598}, {"key": "com/google/android/material/datepicker/DaysOfWeekAdapter.class", "name": "com/google/android/material/datepicker/DaysOfWeekAdapter.class", "size": 3898, "crc": 647393173}, {"key": "com/google/android/material/datepicker/MaterialCalendar$1.class", "name": "com/google/android/material/datepicker/MaterialCalendar$1.class", "size": 1436, "crc": -2045529570}, {"key": "com/google/android/material/datepicker/MaterialCalendar$10.class", "name": "com/google/android/material/datepicker/MaterialCalendar$10.class", "size": 1734, "crc": 1194820086}, {"key": "com/google/android/material/datepicker/MaterialCalendar$11.class", "name": "com/google/android/material/datepicker/MaterialCalendar$11.class", "size": 1160, "crc": 1969913211}, {"key": "com/google/android/material/datepicker/MaterialCalendar$2.class", "name": "com/google/android/material/datepicker/MaterialCalendar$2.class", "size": 1845, "crc": -1495542693}, {"key": "com/google/android/material/datepicker/MaterialCalendar$3.class", "name": "com/google/android/material/datepicker/MaterialCalendar$3.class", "size": 2843, "crc": -1977237246}, {"key": "com/google/android/material/datepicker/MaterialCalendar$4.class", "name": "com/google/android/material/datepicker/MaterialCalendar$4.class", "size": 1328, "crc": -1255527150}, {"key": "com/google/android/material/datepicker/MaterialCalendar$5.class", "name": "com/google/android/material/datepicker/MaterialCalendar$5.class", "size": 4786, "crc": 1111390447}, {"key": "com/google/android/material/datepicker/MaterialCalendar$6.class", "name": "com/google/android/material/datepicker/MaterialCalendar$6.class", "size": 2370, "crc": 1616737285}, {"key": "com/google/android/material/datepicker/MaterialCalendar$7.class", "name": "com/google/android/material/datepicker/MaterialCalendar$7.class", "size": 2746, "crc": -1969464183}, {"key": "com/google/android/material/datepicker/MaterialCalendar$8.class", "name": "com/google/android/material/datepicker/MaterialCalendar$8.class", "size": 1116, "crc": -979054248}, {"key": "com/google/android/material/datepicker/MaterialCalendar$9.class", "name": "com/google/android/material/datepicker/MaterialCalendar$9.class", "size": 1733, "crc": -1038164114}, {"key": "com/google/android/material/datepicker/MaterialCalendar$CalendarSelector.class", "name": "com/google/android/material/datepicker/MaterialCalendar$CalendarSelector.class", "size": 1468, "crc": 523333084}, {"key": "com/google/android/material/datepicker/MaterialCalendar$OnDayClickListener.class", "name": "com/google/android/material/datepicker/MaterialCalendar$OnDayClickListener.class", "size": 312, "crc": -1738189605}, {"key": "com/google/android/material/datepicker/MaterialCalendar.class", "name": "com/google/android/material/datepicker/MaterialCalendar.class", "size": 19791, "crc": 1261619262}, {"key": "com/google/android/material/datepicker/MaterialCalendarGridView$1.class", "name": "com/google/android/material/datepicker/MaterialCalendarGridView$1.class", "size": 1371, "crc": 1975625020}, {"key": "com/google/android/material/datepicker/MaterialCalendarGridView.class", "name": "com/google/android/material/datepicker/MaterialCalendarGridView.class", "size": 9463, "crc": 768397430}, {"key": "com/google/android/material/datepicker/MaterialDatePicker$1.class", "name": "com/google/android/material/datepicker/MaterialDatePicker$1.class", "size": 2260, "crc": 772884273}, {"key": "com/google/android/material/datepicker/MaterialDatePicker$2.class", "name": "com/google/android/material/datepicker/MaterialDatePicker$2.class", "size": 1757, "crc": -1460067814}, {"key": "com/google/android/material/datepicker/MaterialDatePicker$Builder.class", "name": "com/google/android/material/datepicker/MaterialDatePicker$Builder.class", "size": 9483, "crc": -786713870}, {"key": "com/google/android/material/datepicker/MaterialDatePicker$InputMode.class", "name": "com/google/android/material/datepicker/MaterialDatePicker$InputMode.class", "size": 689, "crc": 655469571}, {"key": "com/google/android/material/datepicker/MaterialDatePicker.class", "name": "com/google/android/material/datepicker/MaterialDatePicker.class", "size": 27413, "crc": 2009097299}, {"key": "com/google/android/material/datepicker/MaterialPickerOnPositiveButtonClickListener.class", "name": "com/google/android/material/datepicker/MaterialPickerOnPositiveButtonClickListener.class", "size": 342, "crc": 917769425}, {"key": "com/google/android/material/datepicker/MaterialStyledDatePickerDialog.class", "name": "com/google/android/material/datepicker/MaterialStyledDatePickerDialog.class", "size": 4232, "crc": -1235915523}, {"key": "com/google/android/material/datepicker/MaterialTextInputPicker$1.class", "name": "com/google/android/material/datepicker/MaterialTextInputPicker$1.class", "size": 1840, "crc": -1819939626}, {"key": "com/google/android/material/datepicker/MaterialTextInputPicker.class", "name": "com/google/android/material/datepicker/MaterialTextInputPicker.class", "size": 4962, "crc": 1085738483}, {"key": "com/google/android/material/datepicker/Month$1.class", "name": "com/google/android/material/datepicker/Month$1.class", "size": 1572, "crc": -625585024}, {"key": "com/google/android/material/datepicker/Month.class", "name": "com/google/android/material/datepicker/Month.class", "size": 4906, "crc": 1904957717}, {"key": "com/google/android/material/datepicker/MonthAdapter.class", "name": "com/google/android/material/datepicker/MonthAdapter.class", "size": 11607, "crc": -805163091}, {"key": "com/google/android/material/datepicker/MonthsPagerAdapter$1.class", "name": "com/google/android/material/datepicker/MonthsPagerAdapter$1.class", "size": 2356, "crc": 784347525}, {"key": "com/google/android/material/datepicker/MonthsPagerAdapter$ViewHolder.class", "name": "com/google/android/material/datepicker/MonthsPagerAdapter$ViewHolder.class", "size": 1538, "crc": -870247611}, {"key": "com/google/android/material/datepicker/MonthsPagerAdapter.class", "name": "com/google/android/material/datepicker/MonthsPagerAdapter.class", "size": 8147, "crc": -1180675553}, {"key": "com/google/android/material/datepicker/OnSelectionChangedListener.class", "name": "com/google/android/material/datepicker/OnSelectionChangedListener.class", "size": 1091, "crc": 324408089}, {"key": "com/google/android/material/datepicker/PickerFragment.class", "name": "com/google/android/material/datepicker/PickerFragment.class", "size": 1726, "crc": -942890251}, {"key": "com/google/android/material/datepicker/RangeDateSelector$1.class", "name": "com/google/android/material/datepicker/RangeDateSelector$1.class", "size": 2701, "crc": 61738217}, {"key": "com/google/android/material/datepicker/RangeDateSelector$2.class", "name": "com/google/android/material/datepicker/RangeDateSelector$2.class", "size": 2701, "crc": 1779829346}, {"key": "com/google/android/material/datepicker/RangeDateSelector$3.class", "name": "com/google/android/material/datepicker/RangeDateSelector$3.class", "size": 1964, "crc": -345014480}, {"key": "com/google/android/material/datepicker/RangeDateSelector.class", "name": "com/google/android/material/datepicker/RangeDateSelector.class", "size": 15242, "crc": 167327106}, {"key": "com/google/android/material/datepicker/SingleDateSelector$1.class", "name": "com/google/android/material/datepicker/SingleDateSelector$1.class", "size": 2893, "crc": 1216737585}, {"key": "com/google/android/material/datepicker/SingleDateSelector$2.class", "name": "com/google/android/material/datepicker/SingleDateSelector$2.class", "size": 1922, "crc": -1839578706}, {"key": "com/google/android/material/datepicker/SingleDateSelector.class", "name": "com/google/android/material/datepicker/SingleDateSelector.class", "size": 10207, "crc": 602173761}, {"key": "com/google/android/material/datepicker/SmoothCalendarLayoutManager$1.class", "name": "com/google/android/material/datepicker/SmoothCalendarLayoutManager$1.class", "size": 1366, "crc": -1134337661}, {"key": "com/google/android/material/datepicker/SmoothCalendarLayoutManager.class", "name": "com/google/android/material/datepicker/SmoothCalendarLayoutManager.class", "size": 1749, "crc": 958578966}, {"key": "com/google/android/material/datepicker/TimeSource.class", "name": "com/google/android/material/datepicker/TimeSource.class", "size": 1757, "crc": -111811945}, {"key": "com/google/android/material/datepicker/UtcDates.class", "name": "com/google/android/material/datepicker/UtcDates.class", "size": 8714, "crc": 1441324908}, {"key": "com/google/android/material/datepicker/YearGridAdapter$1.class", "name": "com/google/android/material/datepicker/YearGridAdapter$1.class", "size": 2438, "crc": -1207883988}, {"key": "com/google/android/material/datepicker/YearGridAdapter$ViewHolder.class", "name": "com/google/android/material/datepicker/YearGridAdapter$ViewHolder.class", "size": 703, "crc": 1759153828}, {"key": "com/google/android/material/datepicker/YearGridAdapter.class", "name": "com/google/android/material/datepicker/YearGridAdapter.class", "size": 6133, "crc": -1820527543}, {"key": "com/google/android/material/dialog/InsetDialogOnTouchListener.class", "name": "com/google/android/material/dialog/InsetDialogOnTouchListener.class", "size": 2842, "crc": 215547005}, {"key": "com/google/android/material/dialog/MaterialAlertDialogBuilder.class", "name": "com/google/android/material/dialog/MaterialAlertDialogBuilder.class", "size": 23052, "crc": 1894310561}, {"key": "com/google/android/material/dialog/MaterialDialogs.class", "name": "com/google/android/material/dialog/MaterialDialogs.class", "size": 3363, "crc": -982907301}, {"key": "com/google/android/material/divider/MaterialDivider.class", "name": "com/google/android/material/divider/MaterialDivider.class", "size": 6070, "crc": 111179274}, {"key": "com/google/android/material/divider/MaterialDividerItemDecoration.class", "name": "com/google/android/material/divider/MaterialDividerItemDecoration.class", "size": 10761, "crc": -1930832808}, {"key": "com/google/android/material/dockedtoolbar/DockedToolbarLayout$1.class", "name": "com/google/android/material/dockedtoolbar/DockedToolbarLayout$1.class", "size": 3172, "crc": 1909119264}, {"key": "com/google/android/material/dockedtoolbar/DockedToolbarLayout.class", "name": "com/google/android/material/dockedtoolbar/DockedToolbarLayout.class", "size": 6533, "crc": 368322124}, {"key": "com/google/android/material/drawable/DrawableUtils$OutlineCompatL.class", "name": "com/google/android/material/drawable/DrawableUtils$OutlineCompatL.class", "size": 928, "crc": -358682224}, {"key": "com/google/android/material/drawable/DrawableUtils$OutlineCompatR.class", "name": "com/google/android/material/drawable/DrawableUtils$OutlineCompatR.class", "size": 988, "crc": -1078491542}, {"key": "com/google/android/material/drawable/DrawableUtils.class", "name": "com/google/android/material/drawable/DrawableUtils.class", "size": 10844, "crc": -**********}, {"key": "com/google/android/material/drawable/ScaledDrawableWrapper$ScaledDrawableWrapperState.class", "name": "com/google/android/material/drawable/ScaledDrawableWrapper$ScaledDrawableWrapperState.class", "size": 2783, "crc": -**********}, {"key": "com/google/android/material/drawable/ScaledDrawableWrapper.class", "name": "com/google/android/material/drawable/ScaledDrawableWrapper.class", "size": 2906, "crc": **********}, {"key": "com/google/android/material/elevation/ElevationOverlayProvider.class", "name": "com/google/android/material/elevation/ElevationOverlayProvider.class", "size": 4507, "crc": -613666009}, {"key": "com/google/android/material/elevation/SurfaceColors.class", "name": "com/google/android/material/elevation/SurfaceColors.class", "size": 3041, "crc": **********}, {"key": "com/google/android/material/expandable/ExpandableTransformationWidget.class", "name": "com/google/android/material/expandable/ExpandableTransformationWidget.class", "size": 454, "crc": -**********}, {"key": "com/google/android/material/expandable/ExpandableWidget.class", "name": "com/google/android/material/expandable/ExpandableWidget.class", "size": 208, "crc": -**********}, {"key": "com/google/android/material/expandable/ExpandableWidgetHelper.class", "name": "com/google/android/material/expandable/ExpandableWidgetHelper.class", "size": 2310, "crc": -115629501}, {"key": "com/google/android/material/floatingactionbutton/AnimatorTracker.class", "name": "com/google/android/material/floatingactionbutton/AnimatorTracker.class", "size": 917, "crc": -623246667}, {"key": "com/google/android/material/floatingactionbutton/BaseMotionStrategy$1.class", "name": "com/google/android/material/floatingactionbutton/BaseMotionStrategy$1.class", "size": 3348, "crc": -675517896}, {"key": "com/google/android/material/floatingactionbutton/BaseMotionStrategy.class", "name": "com/google/android/material/floatingactionbutton/BaseMotionStrategy.class", "size": 5665, "crc": -1891165587}, {"key": "com/google/android/material/floatingactionbutton/BorderDrawable$1.class", "name": "com/google/android/material/floatingactionbutton/BorderDrawable$1.class", "size": 288, "crc": -1415629046}, {"key": "com/google/android/material/floatingactionbutton/BorderDrawable$BorderState.class", "name": "com/google/android/material/floatingactionbutton/BorderDrawable$BorderState.class", "size": 1471, "crc": -28166519}, {"key": "com/google/android/material/floatingactionbutton/BorderDrawable.class", "name": "com/google/android/material/floatingactionbutton/BorderDrawable.class", "size": 8249, "crc": 26742236}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$1.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$1.class", "size": 1603, "crc": -2044273499}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$2.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$2.class", "size": 1843, "crc": -1215186748}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$3.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$3.class", "size": 3269, "crc": 625094772}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$4.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$4.class", "size": 2455, "crc": 1501379211}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$5.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$5.class", "size": 2187, "crc": -944146289}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$6.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$6.class", "size": 1969, "crc": 1097027667}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$7.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$7.class", "size": 1970, "crc": 1706037930}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$8.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$8.class", "size": 1921, "crc": 2122825652}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$9.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$9.class", "size": 1921, "crc": 1309973758}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ChangeSizeStrategy.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ChangeSizeStrategy.class", "size": 6464, "crc": -1718623690}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior.class", "size": 9453, "crc": 59225383}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$HideStrategy.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$HideStrategy.class", "size": 2855, "crc": 118193427}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$OnChangedCallback.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$OnChangedCallback.class", "size": 1073, "crc": 645767088}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ShowStrategy.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$ShowStrategy.class", "size": 2791, "crc": -9963252}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$Size.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton$Size.class", "size": 567, "crc": 1775780309}, {"key": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton.class", "name": "com/google/android/material/floatingactionbutton/ExtendedFloatingActionButton.class", "size": 20314, "crc": 1878159817}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$1.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$1.class", "size": 1900, "crc": 410604540}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$BaseBehavior.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$BaseBehavior.class", "size": 9079, "crc": -1165258394}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$Behavior.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$Behavior.class", "size": 2944, "crc": -1612589734}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$OnVisibilityChangedListener.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$OnVisibilityChangedListener.class", "size": 887, "crc": -1697034992}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$ShadowDelegateImpl.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$ShadowDelegateImpl.class", "size": 1971, "crc": 1958118204}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$Size.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$Size.class", "size": 705, "crc": -1705016417}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton$TransformationCallbackWrapper.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton$TransformationCallbackWrapper.class", "size": 2601, "crc": -977414755}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButton.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButton.class", "size": 28134, "crc": -494856779}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$1.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$1.class", "size": 2501, "crc": 260249667}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$2.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$2.class", "size": 2270, "crc": 172836359}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$3.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$3.class", "size": 1593, "crc": 1761510966}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$4.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$4.class", "size": 1723, "crc": 1321737764}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$AlwaysStatefulMaterialShapeDrawable.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$AlwaysStatefulMaterialShapeDrawable.class", "size": 884, "crc": -387225574}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$InternalTransformationCallback.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$InternalTransformationCallback.class", "size": 414, "crc": -434461778}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$InternalVisibilityChangedListener.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl$InternalVisibilityChangedListener.class", "size": 401, "crc": -1526503888}, {"key": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl.class", "name": "com/google/android/material/floatingactionbutton/FloatingActionButtonImpl.class", "size": 27631, "crc": -533383434}, {"key": "com/google/android/material/floatingactionbutton/MotionStrategy.class", "name": "com/google/android/material/floatingactionbutton/MotionStrategy.class", "size": 1583, "crc": 26691178}, {"key": "com/google/android/material/floatingtoolbar/FloatingToolbarLayout$1.class", "name": "com/google/android/material/floatingtoolbar/FloatingToolbarLayout$1.class", "size": 2214, "crc": **********}, {"key": "com/google/android/material/floatingtoolbar/FloatingToolbarLayout.class", "name": "com/google/android/material/floatingtoolbar/FloatingToolbarLayout.class", "size": 7343, "crc": -**********}, {"key": "com/google/android/material/imageview/ShapeableImageView$OutlineProvider.class", "name": "com/google/android/material/imageview/ShapeableImageView$OutlineProvider.class", "size": 2021, "crc": -806225932}, {"key": "com/google/android/material/imageview/ShapeableImageView.class", "name": "com/google/android/material/imageview/ShapeableImageView.class", "size": 13081, "crc": **********}, {"key": "com/google/android/material/internal/BaselineLayout.class", "name": "com/google/android/material/internal/BaselineLayout.class", "size": 3240, "crc": -645083875}, {"key": "com/google/android/material/internal/CheckableGroup$1.class", "name": "com/google/android/material/internal/CheckableGroup$1.class", "size": 1971, "crc": -169056331}, {"key": "com/google/android/material/internal/CheckableGroup$OnCheckedStateChangeListener.class", "name": "com/google/android/material/internal/CheckableGroup$OnCheckedStateChangeListener.class", "size": 493, "crc": -**********}, {"key": "com/google/android/material/internal/CheckableGroup.class", "name": "com/google/android/material/internal/CheckableGroup.class", "size": 7647, "crc": 537003728}, {"key": "com/google/android/material/internal/CheckableImageButton$1.class", "name": "com/google/android/material/internal/CheckableImageButton$1.class", "size": 1734, "crc": -2066462249}, {"key": "com/google/android/material/internal/CheckableImageButton$SavedState$1.class", "name": "com/google/android/material/internal/CheckableImageButton$SavedState$1.class", "size": 2177, "crc": 1688224948}, {"key": "com/google/android/material/internal/CheckableImageButton$SavedState.class", "name": "com/google/android/material/internal/CheckableImageButton$SavedState.class", "size": 1855, "crc": 1142520563}, {"key": "com/google/android/material/internal/CheckableImageButton.class", "name": "com/google/android/material/internal/CheckableImageButton.class", "size": 3781, "crc": 580736151}, {"key": "com/google/android/material/internal/ClippableRoundedCornerLayout.class", "name": "com/google/android/material/internal/ClippableRoundedCornerLayout.class", "size": 3496, "crc": 1871882236}, {"key": "com/google/android/material/internal/CollapsingTextHelper$1.class", "name": "com/google/android/material/internal/CollapsingTextHelper$1.class", "size": 1079, "crc": -403916421}, {"key": "com/google/android/material/internal/CollapsingTextHelper$2.class", "name": "com/google/android/material/internal/CollapsingTextHelper$2.class", "size": 1077, "crc": 714358626}, {"key": "com/google/android/material/internal/CollapsingTextHelper.class", "name": "com/google/android/material/internal/CollapsingTextHelper.class", "size": 32444, "crc": 65196888}, {"key": "com/google/android/material/internal/ContextUtils.class", "name": "com/google/android/material/internal/ContextUtils.class", "size": 1003, "crc": 424202349}, {"key": "com/google/android/material/internal/DescendantOffsetUtils.class", "name": "com/google/android/material/internal/DescendantOffsetUtils.class", "size": 2974, "crc": -1876487382}, {"key": "com/google/android/material/internal/EdgeToEdgeUtils.class", "name": "com/google/android/material/internal/EdgeToEdgeUtils.class", "size": 3995, "crc": 2009390536}, {"key": "com/google/android/material/internal/ExpandCollapseAnimationHelper$1.class", "name": "com/google/android/material/internal/ExpandCollapseAnimationHelper$1.class", "size": 1170, "crc": -312130772}, {"key": "com/google/android/material/internal/ExpandCollapseAnimationHelper$2.class", "name": "com/google/android/material/internal/ExpandCollapseAnimationHelper$2.class", "size": 1171, "crc": 258512733}, {"key": "com/google/android/material/internal/ExpandCollapseAnimationHelper.class", "name": "com/google/android/material/internal/ExpandCollapseAnimationHelper.class", "size": 8773, "crc": 2040341232}, {"key": "com/google/android/material/internal/Experimental.class", "name": "com/google/android/material/internal/Experimental.class", "size": 664, "crc": 2133024842}, {"key": "com/google/android/material/internal/FadeThroughDrawable$1.class", "name": "com/google/android/material/internal/FadeThroughDrawable$1.class", "size": 279, "crc": 906614460}, {"key": "com/google/android/material/internal/FadeThroughDrawable$EmptyDrawable.class", "name": "com/google/android/material/internal/FadeThroughDrawable$EmptyDrawable.class", "size": 1441, "crc": -774547724}, {"key": "com/google/android/material/internal/FadeThroughDrawable.class", "name": "com/google/android/material/internal/FadeThroughDrawable.class", "size": 4258, "crc": -1249118968}, {"key": "com/google/android/material/internal/FadeThroughUpdateListener.class", "name": "com/google/android/material/internal/FadeThroughUpdateListener.class", "size": 1721, "crc": 128268979}, {"key": "com/google/android/material/internal/FadeThroughUtils.class", "name": "com/google/android/material/internal/FadeThroughUtils.class", "size": 743, "crc": -883090361}, {"key": "com/google/android/material/internal/FlowLayout.class", "name": "com/google/android/material/internal/FlowLayout.class", "size": 6907, "crc": 1906720107}, {"key": "com/google/android/material/internal/ForegroundLinearLayout.class", "name": "com/google/android/material/internal/ForegroundLinearLayout.class", "size": 5919, "crc": -1219051995}, {"key": "com/google/android/material/internal/ManufacturerUtils.class", "name": "com/google/android/material/internal/ManufacturerUtils.class", "size": 1565, "crc": 694030990}, {"key": "com/google/android/material/internal/MaterialCheckable$OnCheckedChangeListener.class", "name": "com/google/android/material/internal/MaterialCheckable$OnCheckedChangeListener.class", "size": 426, "crc": -106899930}, {"key": "com/google/android/material/internal/MaterialCheckable.class", "name": "com/google/android/material/internal/MaterialCheckable.class", "size": 1083, "crc": 1976013373}, {"key": "com/google/android/material/internal/MultiViewUpdateListener$Listener.class", "name": "com/google/android/material/internal/MultiViewUpdateListener$Listener.class", "size": 457, "crc": -977408269}, {"key": "com/google/android/material/internal/MultiViewUpdateListener.class", "name": "com/google/android/material/internal/MultiViewUpdateListener.class", "size": 4891, "crc": 1627301299}, {"key": "com/google/android/material/internal/NavigationMenu.class", "name": "com/google/android/material/internal/NavigationMenu.class", "size": 1620, "crc": -1232416306}, {"key": "com/google/android/material/internal/NavigationMenuItemView$1.class", "name": "com/google/android/material/internal/NavigationMenuItemView$1.class", "size": 1272, "crc": 740830703}, {"key": "com/google/android/material/internal/NavigationMenuItemView.class", "name": "com/google/android/material/internal/NavigationMenuItemView.class", "size": 11286, "crc": -1779961283}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$1.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$1.class", "size": 2098, "crc": 1855249899}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$HeaderViewHolder.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$HeaderViewHolder.class", "size": 661, "crc": 1202494260}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuAdapter$1.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuAdapter$1.class", "size": 2086, "crc": 1028901808}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuAdapter.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuAdapter.class", "size": 15318, "crc": 1892625087}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuHeaderItem.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuHeaderItem.class", "size": 658, "crc": 1384412651}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuItem.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuItem.class", "size": 301, "crc": -1141570653}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuSeparatorItem.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuSeparatorItem.class", "size": 949, "crc": 1610068155}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuTextItem.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuTextItem.class", "size": 958, "crc": 1789201280}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuViewAccessibilityDelegate.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NavigationMenuViewAccessibilityDelegate.class", "size": 2058, "crc": 907577158}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$NormalViewHolder.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$NormalViewHolder.class", "size": 1417, "crc": -1364915471}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$SeparatorViewHolder.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$SeparatorViewHolder.class", "size": 1125, "crc": -954390169}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$SubheaderViewHolder.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$SubheaderViewHolder.class", "size": 1125, "crc": 1547869931}, {"key": "com/google/android/material/internal/NavigationMenuPresenter$ViewHolder.class", "name": "com/google/android/material/internal/NavigationMenuPresenter$ViewHolder.class", "size": 658, "crc": 1102312074}, {"key": "com/google/android/material/internal/NavigationMenuPresenter.class", "name": "com/google/android/material/internal/NavigationMenuPresenter.class", "size": 15696, "crc": 1647823218}, {"key": "com/google/android/material/internal/NavigationMenuView.class", "name": "com/google/android/material/internal/NavigationMenuView.class", "size": 1704, "crc": 888911582}, {"key": "com/google/android/material/internal/NavigationSubMenu.class", "name": "com/google/android/material/internal/NavigationSubMenu.class", "size": 1323, "crc": 920342215}, {"key": "com/google/android/material/internal/ParcelableSparseArray$1.class", "name": "com/google/android/material/internal/ParcelableSparseArray$1.class", "size": 2061, "crc": 1970217084}, {"key": "com/google/android/material/internal/ParcelableSparseArray.class", "name": "com/google/android/material/internal/ParcelableSparseArray.class", "size": 2535, "crc": 1931710192}, {"key": "com/google/android/material/internal/ParcelableSparseBooleanArray$1.class", "name": "com/google/android/material/internal/ParcelableSparseBooleanArray$1.class", "size": 2018, "crc": -283481558}, {"key": "com/google/android/material/internal/ParcelableSparseBooleanArray.class", "name": "com/google/android/material/internal/ParcelableSparseBooleanArray.class", "size": 2225, "crc": 182785596}, {"key": "com/google/android/material/internal/ParcelableSparseIntArray$1.class", "name": "com/google/android/material/internal/ParcelableSparseIntArray$1.class", "size": 1941, "crc": 793486839}, {"key": "com/google/android/material/internal/ParcelableSparseIntArray.class", "name": "com/google/android/material/internal/ParcelableSparseIntArray.class", "size": 2132, "crc": 352900708}, {"key": "com/google/android/material/internal/RectEvaluator.class", "name": "com/google/android/material/internal/RectEvaluator.class", "size": 1650, "crc": -1716298055}, {"key": "com/google/android/material/internal/ReversableAnimatedValueInterpolator.class", "name": "com/google/android/material/internal/ReversableAnimatedValueInterpolator.class", "size": 1331, "crc": -455127175}, {"key": "com/google/android/material/internal/ScrimInsetsFrameLayout$1.class", "name": "com/google/android/material/internal/ScrimInsetsFrameLayout$1.class", "size": 2024, "crc": -770021730}, {"key": "com/google/android/material/internal/ScrimInsetsFrameLayout.class", "name": "com/google/android/material/internal/ScrimInsetsFrameLayout.class", "size": 4933, "crc": -1537893585}, {"key": "com/google/android/material/internal/StateListAnimator$1.class", "name": "com/google/android/material/internal/StateListAnimator$1.class", "size": 967, "crc": -1246782369}, {"key": "com/google/android/material/internal/StateListAnimator$Tuple.class", "name": "com/google/android/material/internal/StateListAnimator$Tuple.class", "size": 640, "crc": 689851093}, {"key": "com/google/android/material/internal/StateListAnimator.class", "name": "com/google/android/material/internal/StateListAnimator.class", "size": 2892, "crc": -624728645}, {"key": "com/google/android/material/internal/StaticLayoutBuilderCompat$StaticLayoutBuilderCompatException.class", "name": "com/google/android/material/internal/StaticLayoutBuilderCompat$StaticLayoutBuilderCompatException.class", "size": 1213, "crc": -144698299}, {"key": "com/google/android/material/internal/StaticLayoutBuilderCompat.class", "name": "com/google/android/material/internal/StaticLayoutBuilderCompat.class", "size": 8158, "crc": -1947670256}, {"key": "com/google/android/material/internal/StaticLayoutBuilderConfigurer.class", "name": "com/google/android/material/internal/StaticLayoutBuilderConfigurer.class", "size": 678, "crc": -1106968691}, {"key": "com/google/android/material/internal/TextDrawableHelper$1.class", "name": "com/google/android/material/internal/TextDrawableHelper$1.class", "size": 1814, "crc": -1787989057}, {"key": "com/google/android/material/internal/TextDrawableHelper$TextDrawableDelegate.class", "name": "com/google/android/material/internal/TextDrawableHelper$TextDrawableDelegate.class", "size": 455, "crc": -1810976652}, {"key": "com/google/android/material/internal/TextDrawableHelper.class", "name": "com/google/android/material/internal/TextDrawableHelper.class", "size": 5122, "crc": -1523721320}, {"key": "com/google/android/material/internal/TextScale$1.class", "name": "com/google/android/material/internal/TextScale$1.class", "size": 1529, "crc": 1701824292}, {"key": "com/google/android/material/internal/TextScale.class", "name": "com/google/android/material/internal/TextScale.class", "size": 3040, "crc": 2082481884}, {"key": "com/google/android/material/internal/TextWatcherAdapter.class", "name": "com/google/android/material/internal/TextWatcherAdapter.class", "size": 1248, "crc": 307310181}, {"key": "com/google/android/material/internal/ThemeEnforcement.class", "name": "com/google/android/material/internal/ThemeEnforcement.class", "size": 6438, "crc": 2122506159}, {"key": "com/google/android/material/internal/ToolbarUtils$1.class", "name": "com/google/android/material/internal/ToolbarUtils$1.class", "size": 946, "crc": -550423860}, {"key": "com/google/android/material/internal/ToolbarUtils.class", "name": "com/google/android/material/internal/ToolbarUtils.class", "size": 5552, "crc": 1511788734}, {"key": "com/google/android/material/internal/TouchObserverFrameLayout.class", "name": "com/google/android/material/internal/TouchObserverFrameLayout.class", "size": 1884, "crc": 979513150}, {"key": "com/google/android/material/internal/ViewOverlayImpl.class", "name": "com/google/android/material/internal/ViewOverlayImpl.class", "size": 671, "crc": 29174193}, {"key": "com/google/android/material/internal/ViewUtils$1.class", "name": "com/google/android/material/internal/ViewUtils$1.class", "size": 2373, "crc": -295783308}, {"key": "com/google/android/material/internal/ViewUtils$2.class", "name": "com/google/android/material/internal/ViewUtils$2.class", "size": 1790, "crc": -1994147103}, {"key": "com/google/android/material/internal/ViewUtils$3.class", "name": "com/google/android/material/internal/ViewUtils$3.class", "size": 1061, "crc": 1812802852}, {"key": "com/google/android/material/internal/ViewUtils$4.class", "name": "com/google/android/material/internal/ViewUtils$4.class", "size": 1180, "crc": -1147353545}, {"key": "com/google/android/material/internal/ViewUtils$OnApplyWindowInsetsListener.class", "name": "com/google/android/material/internal/ViewUtils$OnApplyWindowInsetsListener.class", "size": 567, "crc": -247896089}, {"key": "com/google/android/material/internal/ViewUtils$RelativePadding.class", "name": "com/google/android/material/internal/ViewUtils$RelativePadding.class", "size": 1153, "crc": 1668953731}, {"key": "com/google/android/material/internal/ViewUtils.class", "name": "com/google/android/material/internal/ViewUtils.class", "size": 12956, "crc": 946836778}, {"key": "com/google/android/material/internal/VisibilityAwareImageButton.class", "name": "com/google/android/material/internal/VisibilityAwareImageButton.class", "size": 1654, "crc": -1121955043}, {"key": "com/google/android/material/internal/WindowUtils$Api17Impl.class", "name": "com/google/android/material/internal/WindowUtils$Api17Impl.class", "size": 1287, "crc": -1980769455}, {"key": "com/google/android/material/internal/WindowUtils$Api30Impl.class", "name": "com/google/android/material/internal/WindowUtils$Api30Impl.class", "size": 1016, "crc": 733412593}, {"key": "com/google/android/material/internal/WindowUtils.class", "name": "com/google/android/material/internal/WindowUtils.class", "size": 1532, "crc": -1634149425}, {"key": "com/google/android/material/internal/package-info.class", "name": "com/google/android/material/internal/package-info.class", "size": 411, "crc": 1372081028}, {"key": "com/google/android/material/loadingindicator/LoadingIndicator.class", "name": "com/google/android/material/loadingindicator/LoadingIndicator.class", "size": 8826, "crc": 1862671001}, {"key": "com/google/android/material/loadingindicator/LoadingIndicatorAnimatorDelegate$1.class", "name": "com/google/android/material/loadingindicator/LoadingIndicatorAnimatorDelegate$1.class", "size": 1425, "crc": -786386880}, {"key": "com/google/android/material/loadingindicator/LoadingIndicatorAnimatorDelegate$2.class", "name": "com/google/android/material/loadingindicator/LoadingIndicatorAnimatorDelegate$2.class", "size": 1922, "crc": -330585608}, {"key": "com/google/android/material/loadingindicator/LoadingIndicatorAnimatorDelegate$3.class", "name": "com/google/android/material/loadingindicator/LoadingIndicatorAnimatorDelegate$3.class", "size": 1571, "crc": 1420624167}, {"key": "com/google/android/material/loadingindicator/LoadingIndicatorAnimatorDelegate.class", "name": "com/google/android/material/loadingindicator/LoadingIndicatorAnimatorDelegate.class", "size": 7639, "crc": 1006876041}, {"key": "com/google/android/material/loadingindicator/LoadingIndicatorDrawable.class", "name": "com/google/android/material/loadingindicator/LoadingIndicatorDrawable.class", "size": 8656, "crc": -880682595}, {"key": "com/google/android/material/loadingindicator/LoadingIndicatorDrawingDelegate$IndicatorState.class", "name": "com/google/android/material/loadingindicator/LoadingIndicatorDrawingDelegate$IndicatorState.class", "size": 716, "crc": -1024317708}, {"key": "com/google/android/material/loadingindicator/LoadingIndicatorDrawingDelegate.class", "name": "com/google/android/material/loadingindicator/LoadingIndicatorDrawingDelegate.class", "size": 5571, "crc": 1701232573}, {"key": "com/google/android/material/loadingindicator/LoadingIndicatorSpec.class", "name": "com/google/android/material/loadingindicator/LoadingIndicatorSpec.class", "size": 4077, "crc": 2122036023}, {"key": "com/google/android/material/materialswitch/MaterialSwitch.class", "name": "com/google/android/material/materialswitch/MaterialSwitch.class", "size": 10045, "crc": 310378535}, {"key": "com/google/android/material/math/MathUtils.class", "name": "com/google/android/material/math/MathUtils.class", "size": 1951, "crc": -1265290962}, {"key": "com/google/android/material/motion/MaterialBackAnimationHelper.class", "name": "com/google/android/material/motion/MaterialBackAnimationHelper.class", "size": 3455, "crc": -373228366}, {"key": "com/google/android/material/motion/MaterialBackHandler.class", "name": "com/google/android/material/motion/MaterialBackHandler.class", "size": 681, "crc": 133778086}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator$1.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator$1.class", "size": 290, "crc": -529840455}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator$Api33BackCallbackDelegate.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator$Api33BackCallbackDelegate.class", "size": 3410, "crc": -2010555723}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator$Api34BackCallbackDelegate$1.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator$Api34BackCallbackDelegate$1.class", "size": 2262, "crc": -1985844200}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator$Api34BackCallbackDelegate.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator$Api34BackCallbackDelegate.class", "size": 1663, "crc": 1388587842}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator$BackCallbackDelegate.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator$BackCallbackDelegate.class", "size": 597, "crc": -1654628461}, {"key": "com/google/android/material/motion/MaterialBackOrchestrator.class", "name": "com/google/android/material/motion/MaterialBackOrchestrator.class", "size": 3205, "crc": -1557040163}, {"key": "com/google/android/material/motion/MaterialBottomContainerBackHelper$1.class", "name": "com/google/android/material/motion/MaterialBottomContainerBackHelper$1.class", "size": 1326, "crc": -392322150}, {"key": "com/google/android/material/motion/MaterialBottomContainerBackHelper.class", "name": "com/google/android/material/motion/MaterialBottomContainerBackHelper.class", "size": 5877, "crc": -1343846435}, {"key": "com/google/android/material/motion/MaterialMainContainerBackHelper$1.class", "name": "com/google/android/material/motion/MaterialMainContainerBackHelper$1.class", "size": 1219, "crc": -175261888}, {"key": "com/google/android/material/motion/MaterialMainContainerBackHelper.class", "name": "com/google/android/material/motion/MaterialMainContainerBackHelper.class", "size": 11275, "crc": 292902702}, {"key": "com/google/android/material/motion/MaterialSideContainerBackHelper$1.class", "name": "com/google/android/material/motion/MaterialSideContainerBackHelper$1.class", "size": 1603, "crc": 1395496802}, {"key": "com/google/android/material/motion/MaterialSideContainerBackHelper.class", "name": "com/google/android/material/motion/MaterialSideContainerBackHelper.class", "size": 7818, "crc": 1244025295}, {"key": "com/google/android/material/motion/MotionUtils.class", "name": "com/google/android/material/motion/MotionUtils.class", "size": 6250, "crc": 1587914226}, {"key": "com/google/android/material/navigation/DividerMenuItem.class", "name": "com/google/android/material/navigation/DividerMenuItem.class", "size": 5676, "crc": -1891969198}, {"key": "com/google/android/material/navigation/DrawerLayoutUtils$1.class", "name": "com/google/android/material/navigation/DrawerLayoutUtils$1.class", "size": 1304, "crc": 1963916074}, {"key": "com/google/android/material/navigation/DrawerLayoutUtils.class", "name": "com/google/android/material/navigation/DrawerLayoutUtils.class", "size": 2950, "crc": 394311377}, {"key": "com/google/android/material/navigation/NavigationBarDividerView.class", "name": "com/google/android/material/navigation/NavigationBarDividerView.class", "size": 3699, "crc": 306456562}, {"key": "com/google/android/material/navigation/NavigationBarItemView$1.class", "name": "com/google/android/material/navigation/NavigationBarItemView$1.class", "size": 952, "crc": 822287266}, {"key": "com/google/android/material/navigation/NavigationBarItemView$2.class", "name": "com/google/android/material/navigation/NavigationBarItemView$2.class", "size": 1375, "crc": -1059387075}, {"key": "com/google/android/material/navigation/NavigationBarItemView$ActiveIndicatorTransform.class", "name": "com/google/android/material/navigation/NavigationBarItemView$ActiveIndicatorTransform.class", "size": 2125, "crc": -1946952560}, {"key": "com/google/android/material/navigation/NavigationBarItemView$ActiveIndicatorUnlabeledTransform.class", "name": "com/google/android/material/navigation/NavigationBarItemView$ActiveIndicatorUnlabeledTransform.class", "size": 1111, "crc": -939711309}, {"key": "com/google/android/material/navigation/NavigationBarItemView.class", "name": "com/google/android/material/navigation/NavigationBarItemView.class", "size": 36278, "crc": 1339075015}, {"key": "com/google/android/material/navigation/NavigationBarMenu.class", "name": "com/google/android/material/navigation/NavigationBarMenu.class", "size": 3315, "crc": 881213973}, {"key": "com/google/android/material/navigation/NavigationBarMenuBuilder.class", "name": "com/google/android/material/navigation/NavigationBarMenuBuilder.class", "size": 3240, "crc": 1126250726}, {"key": "com/google/android/material/navigation/NavigationBarMenuItemView.class", "name": "com/google/android/material/navigation/NavigationBarMenuItemView.class", "size": 681, "crc": 667934780}, {"key": "com/google/android/material/navigation/NavigationBarMenuView$1.class", "name": "com/google/android/material/navigation/NavigationBarMenuView$1.class", "size": 1981, "crc": -691556953}, {"key": "com/google/android/material/navigation/NavigationBarMenuView.class", "name": "com/google/android/material/navigation/NavigationBarMenuView.class", "size": 33604, "crc": 1767392469}, {"key": "com/google/android/material/navigation/NavigationBarPresenter$SavedState$1.class", "name": "com/google/android/material/navigation/NavigationBarPresenter$SavedState$1.class", "size": 1705, "crc": 1760728869}, {"key": "com/google/android/material/navigation/NavigationBarPresenter$SavedState.class", "name": "com/google/android/material/navigation/NavigationBarPresenter$SavedState.class", "size": 2034, "crc": -2091510117}, {"key": "com/google/android/material/navigation/NavigationBarPresenter.class", "name": "com/google/android/material/navigation/NavigationBarPresenter.class", "size": 4898, "crc": -628978954}, {"key": "com/google/android/material/navigation/NavigationBarSubMenu.class", "name": "com/google/android/material/navigation/NavigationBarSubMenu.class", "size": 1442, "crc": 1916281871}, {"key": "com/google/android/material/navigation/NavigationBarSubheaderView.class", "name": "com/google/android/material/navigation/NavigationBarSubheaderView.class", "size": 4292, "crc": -1948775452}, {"key": "com/google/android/material/navigation/NavigationBarView$1.class", "name": "com/google/android/material/navigation/NavigationBarView$1.class", "size": 2244, "crc": 1338633135}, {"key": "com/google/android/material/navigation/NavigationBarView$ItemGravity.class", "name": "com/google/android/material/navigation/NavigationBarView$ItemGravity.class", "size": 690, "crc": 1625069538}, {"key": "com/google/android/material/navigation/NavigationBarView$ItemIconGravity.class", "name": "com/google/android/material/navigation/NavigationBarView$ItemIconGravity.class", "size": 698, "crc": 948385811}, {"key": "com/google/android/material/navigation/NavigationBarView$LabelVisibility.class", "name": "com/google/android/material/navigation/NavigationBarView$LabelVisibility.class", "size": 698, "crc": -1171907660}, {"key": "com/google/android/material/navigation/NavigationBarView$OnItemReselectedListener.class", "name": "com/google/android/material/navigation/NavigationBarView$OnItemReselectedListener.class", "size": 449, "crc": -1490258593}, {"key": "com/google/android/material/navigation/NavigationBarView$OnItemSelectedListener.class", "name": "com/google/android/material/navigation/NavigationBarView$OnItemSelectedListener.class", "size": 443, "crc": -1572936671}, {"key": "com/google/android/material/navigation/NavigationBarView$SavedState$1.class", "name": "com/google/android/material/navigation/NavigationBarView$SavedState$1.class", "size": 2199, "crc": -1485012346}, {"key": "com/google/android/material/navigation/NavigationBarView$SavedState.class", "name": "com/google/android/material/navigation/NavigationBarView$SavedState.class", "size": 2141, "crc": -1628654638}, {"key": "com/google/android/material/navigation/NavigationBarView.class", "name": "com/google/android/material/navigation/NavigationBarView.class", "size": 25939, "crc": 1215768626}, {"key": "com/google/android/material/navigation/NavigationView$1.class", "name": "com/google/android/material/navigation/NavigationView$1.class", "size": 2226, "crc": -1543837076}, {"key": "com/google/android/material/navigation/NavigationView$2.class", "name": "com/google/android/material/navigation/NavigationView$2.class", "size": 1603, "crc": -641187160}, {"key": "com/google/android/material/navigation/NavigationView$3.class", "name": "com/google/android/material/navigation/NavigationView$3.class", "size": 3230, "crc": 1755074887}, {"key": "com/google/android/material/navigation/NavigationView$OnNavigationItemSelectedListener.class", "name": "com/google/android/material/navigation/NavigationView$OnNavigationItemSelectedListener.class", "size": 454, "crc": -470778200}, {"key": "com/google/android/material/navigation/NavigationView$SavedState$1.class", "name": "com/google/android/material/navigation/NavigationView$SavedState$1.class", "size": 2172, "crc": -1607932714}, {"key": "com/google/android/material/navigation/NavigationView$SavedState.class", "name": "com/google/android/material/navigation/NavigationView$SavedState.class", "size": 1753, "crc": -628699638}, {"key": "com/google/android/material/navigation/NavigationView.class", "name": "com/google/android/material/navigation/NavigationView.class", "size": 31979, "crc": 1788217332}, {"key": "com/google/android/material/navigationrail/LabelMoveTransition.class", "name": "com/google/android/material/navigationrail/LabelMoveTransition.class", "size": 3212, "crc": 240196000}, {"key": "com/google/android/material/navigationrail/NavigationRailFrameLayout.class", "name": "com/google/android/material/navigationrail/NavigationRailFrameLayout.class", "size": 3110, "crc": -2050551342}, {"key": "com/google/android/material/navigationrail/NavigationRailItemView.class", "name": "com/google/android/material/navigationrail/NavigationRailItemView.class", "size": 1942, "crc": -606024909}, {"key": "com/google/android/material/navigationrail/NavigationRailMenuView.class", "name": "com/google/android/material/navigationrail/NavigationRailMenuView.class", "size": 5751, "crc": -**********}, {"key": "com/google/android/material/navigationrail/NavigationRailView$1.class", "name": "com/google/android/material/navigationrail/NavigationRailView$1.class", "size": 2663, "crc": 362399103}, {"key": "com/google/android/material/navigationrail/NavigationRailView.class", "name": "com/google/android/material/navigationrail/NavigationRailView.class", "size": 18675, "crc": **********}, {"key": "com/google/android/material/progressindicator/AnimatorDurationScaleProvider.class", "name": "com/google/android/material/progressindicator/AnimatorDurationScaleProvider.class", "size": 1151, "crc": -526853570}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$1.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$1.class", "size": 919, "crc": 451931450}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$2.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$2.class", "size": 1042, "crc": **********}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$3.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$3.class", "size": 1433, "crc": **********}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$4.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$4.class", "size": 1431, "crc": -**********}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$HideAnimationBehavior.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$HideAnimationBehavior.class", "size": 736, "crc": -109376717}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator$ShowAnimationBehavior.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator$ShowAnimationBehavior.class", "size": 736, "crc": 232852297}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicator.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicator.class", "size": 21088, "crc": -377524108}, {"key": "com/google/android/material/progressindicator/BaseProgressIndicatorSpec.class", "name": "com/google/android/material/progressindicator/BaseProgressIndicatorSpec.class", "size": 6618, "crc": -1504044979}, {"key": "com/google/android/material/progressindicator/CircularDrawingDelegate.class", "name": "com/google/android/material/progressindicator/CircularDrawingDelegate.class", "size": 16058, "crc": -1737159403}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateAdvanceAnimatorDelegate$1.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateAdvanceAnimatorDelegate$1.class", "size": 1700, "crc": -184068316}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateAdvanceAnimatorDelegate$2.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateAdvanceAnimatorDelegate$2.class", "size": 1659, "crc": -1479129142}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateAdvanceAnimatorDelegate$3.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateAdvanceAnimatorDelegate$3.class", "size": 2038, "crc": 742973519}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateAdvanceAnimatorDelegate$4.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateAdvanceAnimatorDelegate$4.class", "size": 2120, "crc": 1129377038}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateAdvanceAnimatorDelegate.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateAdvanceAnimatorDelegate.class", "size": 9194, "crc": 188968275}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateRetreatAnimatorDelegate$1.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateRetreatAnimatorDelegate$1.class", "size": 1737, "crc": -542599372}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateRetreatAnimatorDelegate$2.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateRetreatAnimatorDelegate$2.class", "size": 1659, "crc": 1722298622}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateRetreatAnimatorDelegate$3.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateRetreatAnimatorDelegate$3.class", "size": 2038, "crc": -1285428785}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateRetreatAnimatorDelegate$4.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateRetreatAnimatorDelegate$4.class", "size": 2120, "crc": 304405017}, {"key": "com/google/android/material/progressindicator/CircularIndeterminateRetreatAnimatorDelegate.class", "name": "com/google/android/material/progressindicator/CircularIndeterminateRetreatAnimatorDelegate.class", "size": 9967, "crc": -493343623}, {"key": "com/google/android/material/progressindicator/CircularProgressIndicator$IndeterminateAnimationType.class", "name": "com/google/android/material/progressindicator/CircularProgressIndicator$IndeterminateAnimationType.class", "size": 758, "crc": 246979263}, {"key": "com/google/android/material/progressindicator/CircularProgressIndicator$IndicatorDirection.class", "name": "com/google/android/material/progressindicator/CircularProgressIndicator$IndicatorDirection.class", "size": 742, "crc": -1099650353}, {"key": "com/google/android/material/progressindicator/CircularProgressIndicator.class", "name": "com/google/android/material/progressindicator/CircularProgressIndicator.class", "size": 7017, "crc": 157389880}, {"key": "com/google/android/material/progressindicator/CircularProgressIndicatorSpec.class", "name": "com/google/android/material/progressindicator/CircularProgressIndicatorSpec.class", "size": 3368, "crc": 702124148}, {"key": "com/google/android/material/progressindicator/DeterminateDrawable$1.class", "name": "com/google/android/material/progressindicator/DeterminateDrawable$1.class", "size": 1887, "crc": -105251767}, {"key": "com/google/android/material/progressindicator/DeterminateDrawable.class", "name": "com/google/android/material/progressindicator/DeterminateDrawable.class", "size": 19000, "crc": 1210949103}, {"key": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$1.class", "name": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$1.class", "size": 1128, "crc": 1914661636}, {"key": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$2.class", "name": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$2.class", "size": 1255, "crc": -408756000}, {"key": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$3.class", "name": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange$3.class", "size": 1876, "crc": -645596262}, {"key": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange.class", "name": "com/google/android/material/progressindicator/DrawableWithAnimatedVisibilityChange.class", "size": 11685, "crc": -1087595448}, {"key": "com/google/android/material/progressindicator/DrawingDelegate$ActiveIndicator.class", "name": "com/google/android/material/progressindicator/DrawingDelegate$ActiveIndicator.class", "size": 989, "crc": 1483041166}, {"key": "com/google/android/material/progressindicator/DrawingDelegate$PathPoint.class", "name": "com/google/android/material/progressindicator/DrawingDelegate$PathPoint.class", "size": 3332, "crc": -146197873}, {"key": "com/google/android/material/progressindicator/DrawingDelegate.class", "name": "com/google/android/material/progressindicator/DrawingDelegate.class", "size": 3196, "crc": 2100013227}, {"key": "com/google/android/material/progressindicator/IndeterminateAnimatorDelegate.class", "name": "com/google/android/material/progressindicator/IndeterminateAnimatorDelegate.class", "size": 2534, "crc": -473907994}, {"key": "com/google/android/material/progressindicator/IndeterminateDrawable.class", "name": "com/google/android/material/progressindicator/IndeterminateDrawable.class", "size": 15923, "crc": 357639233}, {"key": "com/google/android/material/progressindicator/LinearDrawingDelegate.class", "name": "com/google/android/material/progressindicator/LinearDrawingDelegate.class", "size": 14612, "crc": -2024520016}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate$1.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate$1.class", "size": 1845, "crc": 1717828576}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate$2.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate$2.class", "size": 2047, "crc": -28575435}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateContiguousAnimatorDelegate.class", "size": 7312, "crc": -1036034706}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$1.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$1.class", "size": 1817, "crc": 996054081}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$2.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$2.class", "size": 1653, "crc": 1524663513}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$3.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate$3.class", "size": 2029, "crc": -659395873}, {"key": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate.class", "name": "com/google/android/material/progressindicator/LinearIndeterminateDisjointAnimatorDelegate.class", "size": 8455, "crc": -549571169}, {"key": "com/google/android/material/progressindicator/LinearProgressIndicator$IndeterminateAnimationType.class", "name": "com/google/android/material/progressindicator/LinearProgressIndicator$IndeterminateAnimationType.class", "size": 752, "crc": -974668980}, {"key": "com/google/android/material/progressindicator/LinearProgressIndicator$IndicatorDirection.class", "name": "com/google/android/material/progressindicator/LinearProgressIndicator$IndicatorDirection.class", "size": 736, "crc": 1867071435}, {"key": "com/google/android/material/progressindicator/LinearProgressIndicator.class", "name": "com/google/android/material/progressindicator/LinearProgressIndicator.class", "size": 9220, "crc": -1140644249}, {"key": "com/google/android/material/progressindicator/LinearProgressIndicatorSpec.class", "name": "com/google/android/material/progressindicator/LinearProgressIndicatorSpec.class", "size": 4805, "crc": -1734209204}, {"key": "com/google/android/material/radiobutton/MaterialRadioButton.class", "name": "com/google/android/material/radiobutton/MaterialRadioButton.class", "size": 4289, "crc": 1638443831}, {"key": "com/google/android/material/resources/CancelableFontCallback$ApplyFont.class", "name": "com/google/android/material/resources/CancelableFontCallback$ApplyFont.class", "size": 331, "crc": 1042648903}, {"key": "com/google/android/material/resources/CancelableFontCallback.class", "name": "com/google/android/material/resources/CancelableFontCallback.class", "size": 1715, "crc": -5903470}, {"key": "com/google/android/material/resources/MaterialAttributes.class", "name": "com/google/android/material/resources/MaterialAttributes.class", "size": 4527, "crc": 1435983056}, {"key": "com/google/android/material/resources/MaterialResources.class", "name": "com/google/android/material/resources/MaterialResources.class", "size": 6400, "crc": 330082741}, {"key": "com/google/android/material/resources/TextAppearance$1.class", "name": "com/google/android/material/resources/TextAppearance$1.class", "size": 2074, "crc": 1766524993}, {"key": "com/google/android/material/resources/TextAppearance$2.class", "name": "com/google/android/material/resources/TextAppearance$2.class", "size": 1789, "crc": -1739062711}, {"key": "com/google/android/material/resources/TextAppearance.class", "name": "com/google/android/material/resources/TextAppearance.class", "size": 13029, "crc": 2101019361}, {"key": "com/google/android/material/resources/TextAppearanceConfig.class", "name": "com/google/android/material/resources/TextAppearanceConfig.class", "size": 657, "crc": -292031691}, {"key": "com/google/android/material/resources/TextAppearanceFontCallback.class", "name": "com/google/android/material/resources/TextAppearanceFontCallback.class", "size": 747, "crc": 1816268200}, {"key": "com/google/android/material/resources/TypefaceUtils.class", "name": "com/google/android/material/resources/TypefaceUtils.class", "size": 2095, "crc": -1959060921}, {"key": "com/google/android/material/ripple/RippleDrawableCompat$1.class", "name": "com/google/android/material/ripple/RippleDrawableCompat$1.class", "size": 278, "crc": 250906177}, {"key": "com/google/android/material/ripple/RippleDrawableCompat$RippleDrawableCompatState.class", "name": "com/google/android/material/ripple/RippleDrawableCompat$RippleDrawableCompatState.class", "size": 1951, "crc": 1517529752}, {"key": "com/google/android/material/ripple/RippleDrawableCompat.class", "name": "com/google/android/material/ripple/RippleDrawableCompat.class", "size": 5837, "crc": -1010435104}, {"key": "com/google/android/material/ripple/RippleUtils$RippleUtilsLollipop.class", "name": "com/google/android/material/ripple/RippleUtils$RippleUtilsLollipop.class", "size": 1998, "crc": 117131760}, {"key": "com/google/android/material/ripple/RippleUtils.class", "name": "com/google/android/material/ripple/RippleUtils.class", "size": 4090, "crc": 1293571149}, {"key": "com/google/android/material/search/SearchBar$1.class", "name": "com/google/android/material/search/SearchBar$1.class", "size": 1691, "crc": -975526647}, {"key": "com/google/android/material/search/SearchBar$OnLoadAnimationCallback.class", "name": "com/google/android/material/search/SearchBar$OnLoadAnimationCallback.class", "size": 627, "crc": -1247268741}, {"key": "com/google/android/material/search/SearchBar$SavedState$1.class", "name": "com/google/android/material/search/SearchBar$SavedState$1.class", "size": 1871, "crc": 35693771}, {"key": "com/google/android/material/search/SearchBar$SavedState.class", "name": "com/google/android/material/search/SearchBar$SavedState.class", "size": 1694, "crc": 1272791485}, {"key": "com/google/android/material/search/SearchBar$ScrollingViewBehavior.class", "name": "com/google/android/material/search/SearchBar$ScrollingViewBehavior.class", "size": 2178, "crc": -993584042}, {"key": "com/google/android/material/search/SearchBar.class", "name": "com/google/android/material/search/SearchBar.class", "size": 29519, "crc": -882869863}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$1.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$1.class", "size": 2054, "crc": -1908025371}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$2.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$2.class", "size": 1262, "crc": -1743803405}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$3.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$3.class", "size": 1191, "crc": 1660127652}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$4.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$4.class", "size": 1502, "crc": -1554246593}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$5.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$5.class", "size": 1193, "crc": 1044038595}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$6.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$6.class", "size": 1547, "crc": -1692127525}, {"key": "com/google/android/material/search/SearchBarAnimationHelper$OnLoadAnimationInvocation.class", "name": "com/google/android/material/search/SearchBarAnimationHelper$OnLoadAnimationInvocation.class", "size": 565, "crc": -722548086}, {"key": "com/google/android/material/search/SearchBarAnimationHelper.class", "name": "com/google/android/material/search/SearchBarAnimationHelper.class", "size": 17907, "crc": 1024104916}, {"key": "com/google/android/material/search/SearchView$1.class", "name": "com/google/android/material/search/SearchView$1.class", "size": 1415, "crc": 686816335}, {"key": "com/google/android/material/search/SearchView$Behavior.class", "name": "com/google/android/material/search/SearchView$Behavior.class", "size": 1918, "crc": 597455565}, {"key": "com/google/android/material/search/SearchView$SavedState$1.class", "name": "com/google/android/material/search/SearchView$SavedState$1.class", "size": 1880, "crc": 1948015241}, {"key": "com/google/android/material/search/SearchView$SavedState.class", "name": "com/google/android/material/search/SearchView$SavedState.class", "size": 1809, "crc": 2009409298}, {"key": "com/google/android/material/search/SearchView$TransitionListener.class", "name": "com/google/android/material/search/SearchView$TransitionListener.class", "size": 651, "crc": -1453001187}, {"key": "com/google/android/material/search/SearchView$TransitionState.class", "name": "com/google/android/material/search/SearchView$TransitionState.class", "size": 1496, "crc": 1107073148}, {"key": "com/google/android/material/search/SearchView.class", "name": "com/google/android/material/search/SearchView.class", "size": 33617, "crc": 968735846}, {"key": "com/google/android/material/search/SearchViewAnimationHelper$1.class", "name": "com/google/android/material/search/SearchViewAnimationHelper$1.class", "size": 2172, "crc": -1066903869}, {"key": "com/google/android/material/search/SearchViewAnimationHelper$2.class", "name": "com/google/android/material/search/SearchViewAnimationHelper$2.class", "size": 2006, "crc": -1691132172}, {"key": "com/google/android/material/search/SearchViewAnimationHelper$3.class", "name": "com/google/android/material/search/SearchViewAnimationHelper$3.class", "size": 1980, "crc": -471079836}, {"key": "com/google/android/material/search/SearchViewAnimationHelper$4.class", "name": "com/google/android/material/search/SearchViewAnimationHelper$4.class", "size": 2007, "crc": 595294866}, {"key": "com/google/android/material/search/SearchViewAnimationHelper$5.class", "name": "com/google/android/material/search/SearchViewAnimationHelper$5.class", "size": 2477, "crc": -923093281}, {"key": "com/google/android/material/search/SearchViewAnimationHelper.class", "name": "com/google/android/material/search/SearchViewAnimationHelper.class", "size": 28369, "crc": -1154415092}, {"key": "com/google/android/material/shadow/ShadowDrawableWrapper.class", "name": "com/google/android/material/shadow/ShadowDrawableWrapper.class", "size": 9309, "crc": -1232079891}, {"key": "com/google/android/material/shadow/ShadowRenderer.class", "name": "com/google/android/material/shadow/ShadowRenderer.class", "size": 5630, "crc": -382384018}, {"key": "com/google/android/material/shadow/ShadowViewDelegate.class", "name": "com/google/android/material/shadow/ShadowViewDelegate.class", "size": 413, "crc": 1244519704}, {"key": "com/google/android/material/shape/AbsoluteCornerSize.class", "name": "com/google/android/material/shape/AbsoluteCornerSize.class", "size": 1544, "crc": -1651678152}, {"key": "com/google/android/material/shape/AdjustedCornerSize.class", "name": "com/google/android/material/shape/AdjustedCornerSize.class", "size": 1789, "crc": 265138031}, {"key": "com/google/android/material/shape/ClampedCornerSize.class", "name": "com/google/android/material/shape/ClampedCornerSize.class", "size": 2136, "crc": 1402030370}, {"key": "com/google/android/material/shape/CornerFamily.class", "name": "com/google/android/material/shape/CornerFamily.class", "size": 396, "crc": 725531220}, {"key": "com/google/android/material/shape/CornerSize.class", "name": "com/google/android/material/shape/CornerSize.class", "size": 273, "crc": 1791305283}, {"key": "com/google/android/material/shape/CornerTreatment.class", "name": "com/google/android/material/shape/CornerTreatment.class", "size": 1441, "crc": -1553233941}, {"key": "com/google/android/material/shape/CutCornerTreatment.class", "name": "com/google/android/material/shape/CutCornerTreatment.class", "size": 1218, "crc": 318109768}, {"key": "com/google/android/material/shape/EdgeTreatment.class", "name": "com/google/android/material/shape/EdgeTreatment.class", "size": 1139, "crc": 733080170}, {"key": "com/google/android/material/shape/InterpolateOnScrollPositionChangeHelper$1.class", "name": "com/google/android/material/shape/InterpolateOnScrollPositionChangeHelper$1.class", "size": 1048, "crc": -1378987014}, {"key": "com/google/android/material/shape/InterpolateOnScrollPositionChangeHelper.class", "name": "com/google/android/material/shape/InterpolateOnScrollPositionChangeHelper.class", "size": 3302, "crc": 884805252}, {"key": "com/google/android/material/shape/MarkerEdgeTreatment.class", "name": "com/google/android/material/shape/MarkerEdgeTreatment.class", "size": 1245, "crc": 1182416569}, {"key": "com/google/android/material/shape/MaterialShapeDrawable$1.class", "name": "com/google/android/material/shape/MaterialShapeDrawable$1.class", "size": 1598, "crc": -1203982289}, {"key": "com/google/android/material/shape/MaterialShapeDrawable$2.class", "name": "com/google/android/material/shape/MaterialShapeDrawable$2.class", "size": 2394, "crc": 1144708373}, {"key": "com/google/android/material/shape/MaterialShapeDrawable$CompatibilityShadowMode.class", "name": "com/google/android/material/shape/MaterialShapeDrawable$CompatibilityShadowMode.class", "size": 478, "crc": -1536531433}, {"key": "com/google/android/material/shape/MaterialShapeDrawable$MaterialShapeDrawableState.class", "name": "com/google/android/material/shape/MaterialShapeDrawable$MaterialShapeDrawableState.class", "size": 4038, "crc": -1949072974}, {"key": "com/google/android/material/shape/MaterialShapeDrawable$OnCornerSizeChangeListener.class", "name": "com/google/android/material/shape/MaterialShapeDrawable$OnCornerSizeChangeListener.class", "size": 587, "crc": -2050395922}, {"key": "com/google/android/material/shape/MaterialShapeDrawable$SpringAnimatedCornerSizeProperty.class", "name": "com/google/android/material/shape/MaterialShapeDrawable$SpringAnimatedCornerSizeProperty.class", "size": 2423, "crc": 413639650}, {"key": "com/google/android/material/shape/MaterialShapeDrawable.class", "name": "com/google/android/material/shape/MaterialShapeDrawable.class", "size": 36978, "crc": -304979251}, {"key": "com/google/android/material/shape/MaterialShapeUtils.class", "name": "com/google/android/material/shape/MaterialShapeUtils.class", "size": 2315, "crc": -38044507}, {"key": "com/google/android/material/shape/MaterialShapes$1.class", "name": "com/google/android/material/shape/MaterialShapes$1.class", "size": 258, "crc": 712629753}, {"key": "com/google/android/material/shape/MaterialShapes$VertexAndRounding.class", "name": "com/google/android/material/shape/MaterialShapes$VertexAndRounding.class", "size": 2806, "crc": -915099626}, {"key": "com/google/android/material/shape/MaterialShapes.class", "name": "com/google/android/material/shape/MaterialShapes.class", "size": 20225, "crc": 781910894}, {"key": "com/google/android/material/shape/OffsetEdgeTreatment.class", "name": "com/google/android/material/shape/OffsetEdgeTreatment.class", "size": 1083, "crc": 857766351}, {"key": "com/google/android/material/shape/RelativeCornerSize.class", "name": "com/google/android/material/shape/RelativeCornerSize.class", "size": 2561, "crc": -1180736977}, {"key": "com/google/android/material/shape/RoundedCornerTreatment.class", "name": "com/google/android/material/shape/RoundedCornerTreatment.class", "size": 1129, "crc": 905665024}, {"key": "com/google/android/material/shape/ShapeAppearanceModel$1.class", "name": "com/google/android/material/shape/ShapeAppearanceModel$1.class", "size": 276, "crc": -1441062337}, {"key": "com/google/android/material/shape/ShapeAppearanceModel$Builder.class", "name": "com/google/android/material/shape/ShapeAppearanceModel$Builder.class", "size": 9315, "crc": -49507928}, {"key": "com/google/android/material/shape/ShapeAppearanceModel$CornerSizeUnaryOperator.class", "name": "com/google/android/material/shape/ShapeAppearanceModel$CornerSizeUnaryOperator.class", "size": 751, "crc": -880720696}, {"key": "com/google/android/material/shape/ShapeAppearanceModel.class", "name": "com/google/android/material/shape/ShapeAppearanceModel.class", "size": 12838, "crc": -240657521}, {"key": "com/google/android/material/shape/ShapeAppearancePathProvider$Lazy.class", "name": "com/google/android/material/shape/ShapeAppearancePathProvider$Lazy.class", "size": 649, "crc": -**********}, {"key": "com/google/android/material/shape/ShapeAppearancePathProvider$PathListener.class", "name": "com/google/android/material/shape/ShapeAppearancePathProvider$PathListener.class", "size": 676, "crc": -704714075}, {"key": "com/google/android/material/shape/ShapeAppearancePathProvider$ShapeAppearancePathSpec.class", "name": "com/google/android/material/shape/ShapeAppearancePathProvider$ShapeAppearancePathSpec.class", "size": 1498, "crc": -788633045}, {"key": "com/google/android/material/shape/ShapeAppearancePathProvider.class", "name": "com/google/android/material/shape/ShapeAppearancePathProvider.class", "size": 10747, "crc": **********}, {"key": "com/google/android/material/shape/ShapePath$1.class", "name": "com/google/android/material/shape/ShapePath$1.class", "size": 1691, "crc": 575834400}, {"key": "com/google/android/material/shape/ShapePath$ArcShadowOperation.class", "name": "com/google/android/material/shape/ShapePath$ArcShadowOperation.class", "size": 1903, "crc": -660118962}, {"key": "com/google/android/material/shape/ShapePath$InnerCornerShadowOperation.class", "name": "com/google/android/material/shape/ShapePath$InnerCornerShadowOperation.class", "size": 3417, "crc": -**********}, {"key": "com/google/android/material/shape/ShapePath$LineShadowOperation.class", "name": "com/google/android/material/shape/ShapePath$LineShadowOperation.class", "size": 2306, "crc": -**********}, {"key": "com/google/android/material/shape/ShapePath$PathArcOperation.class", "name": "com/google/android/material/shape/ShapePath$PathArcOperation.class", "size": 3609, "crc": -1580002114}, {"key": "com/google/android/material/shape/ShapePath$PathCubicOperation.class", "name": "com/google/android/material/shape/ShapePath$PathCubicOperation.class", "size": 2469, "crc": 940646983}, {"key": "com/google/android/material/shape/ShapePath$PathLineOperation.class", "name": "com/google/android/material/shape/ShapePath$PathLineOperation.class", "size": 1612, "crc": -1067321173}, {"key": "com/google/android/material/shape/ShapePath$PathOperation.class", "name": "com/google/android/material/shape/ShapePath$PathOperation.class", "size": 624, "crc": -1118920582}, {"key": "com/google/android/material/shape/ShapePath$PathQuadOperation.class", "name": "com/google/android/material/shape/ShapePath$PathQuadOperation.class", "size": 2512, "crc": -1737343446}, {"key": "com/google/android/material/shape/ShapePath$ShadowCompatOperation.class", "name": "com/google/android/material/shape/ShapePath$ShadowCompatOperation.class", "size": 1121, "crc": 2095096972}, {"key": "com/google/android/material/shape/ShapePath.class", "name": "com/google/android/material/shape/ShapePath.class", "size": 9014, "crc": 544709}, {"key": "com/google/android/material/shape/ShapePathModel.class", "name": "com/google/android/material/shape/ShapePathModel.class", "size": 2831, "crc": 332949377}, {"key": "com/google/android/material/shape/Shapeable.class", "name": "com/google/android/material/shape/Shapeable.class", "size": 450, "crc": -1953477021}, {"key": "com/google/android/material/shape/ShapeableDelegate.class", "name": "com/google/android/material/shape/ShapeableDelegate.class", "size": 4169, "crc": -1367133933}, {"key": "com/google/android/material/shape/ShapeableDelegateV14.class", "name": "com/google/android/material/shape/ShapeableDelegateV14.class", "size": 1028, "crc": -488580410}, {"key": "com/google/android/material/shape/ShapeableDelegateV22$1.class", "name": "com/google/android/material/shape/ShapeableDelegateV22$1.class", "size": 1492, "crc": -2120750479}, {"key": "com/google/android/material/shape/ShapeableDelegateV22.class", "name": "com/google/android/material/shape/ShapeableDelegateV22.class", "size": 4246, "crc": -2075291107}, {"key": "com/google/android/material/shape/ShapeableDelegateV33$1.class", "name": "com/google/android/material/shape/ShapeableDelegateV33$1.class", "size": 1187, "crc": -1378542422}, {"key": "com/google/android/material/shape/ShapeableDelegateV33.class", "name": "com/google/android/material/shape/ShapeableDelegateV33.class", "size": 1510, "crc": 1899771739}, {"key": "com/google/android/material/shape/StateListCornerSize.class", "name": "com/google/android/material/shape/StateListCornerSize.class", "size": 7997, "crc": 2064123330}, {"key": "com/google/android/material/shape/StateListShapeAppearanceModel$1.class", "name": "com/google/android/material/shape/StateListShapeAppearanceModel$1.class", "size": 303, "crc": 511500049}, {"key": "com/google/android/material/shape/StateListShapeAppearanceModel$Builder.class", "name": "com/google/android/material/shape/StateListShapeAppearanceModel$Builder.class", "size": 8305, "crc": -1947945293}, {"key": "com/google/android/material/shape/StateListShapeAppearanceModel.class", "name": "com/google/android/material/shape/StateListShapeAppearanceModel.class", "size": 10406, "crc": 98728480}, {"key": "com/google/android/material/shape/StateListSizeChange$SizeChange.class", "name": "com/google/android/material/shape/StateListSizeChange$SizeChange.class", "size": 1441, "crc": -568591500}, {"key": "com/google/android/material/shape/StateListSizeChange$SizeChangeAmount.class", "name": "com/google/android/material/shape/StateListSizeChange$SizeChangeAmount.class", "size": 1137, "crc": -834176610}, {"key": "com/google/android/material/shape/StateListSizeChange$SizeChangeType.class", "name": "com/google/android/material/shape/StateListSizeChange$SizeChangeType.class", "size": 1449, "crc": 367410551}, {"key": "com/google/android/material/shape/StateListSizeChange.class", "name": "com/google/android/material/shape/StateListSizeChange.class", "size": 8575, "crc": -155376658}, {"key": "com/google/android/material/shape/TriangleEdgeTreatment.class", "name": "com/google/android/material/shape/TriangleEdgeTreatment.class", "size": 1082, "crc": -44899577}, {"key": "com/google/android/material/sidesheet/LeftSheetDelegate.class", "name": "com/google/android/material/sidesheet/LeftSheetDelegate.class", "size": 4289, "crc": 2115132830}, {"key": "com/google/android/material/sidesheet/RightSheetDelegate.class", "name": "com/google/android/material/sidesheet/RightSheetDelegate.class", "size": 4287, "crc": 1817874061}, {"key": "com/google/android/material/sidesheet/Sheet$SheetEdge.class", "name": "com/google/android/material/sidesheet/Sheet$SheetEdge.class", "size": 648, "crc": -231123810}, {"key": "com/google/android/material/sidesheet/Sheet$SheetState.class", "name": "com/google/android/material/sidesheet/Sheet$SheetState.class", "size": 650, "crc": 185280578}, {"key": "com/google/android/material/sidesheet/Sheet$StableSheetState.class", "name": "com/google/android/material/sidesheet/Sheet$StableSheetState.class", "size": 662, "crc": -918598689}, {"key": "com/google/android/material/sidesheet/Sheet.class", "name": "com/google/android/material/sidesheet/Sheet.class", "size": 1036, "crc": 1356787733}, {"key": "com/google/android/material/sidesheet/SheetCallback.class", "name": "com/google/android/material/sidesheet/SheetCallback.class", "size": 341, "crc": -1845064961}, {"key": "com/google/android/material/sidesheet/SheetDelegate.class", "name": "com/google/android/material/sidesheet/SheetDelegate.class", "size": 1534, "crc": 640332383}, {"key": "com/google/android/material/sidesheet/SheetDialog$1.class", "name": "com/google/android/material/sidesheet/SheetDialog$1.class", "size": 1860, "crc": -1666444248}, {"key": "com/google/android/material/sidesheet/SheetDialog.class", "name": "com/google/android/material/sidesheet/SheetDialog.class", "size": 11172, "crc": -906041208}, {"key": "com/google/android/material/sidesheet/SheetUtils.class", "name": "com/google/android/material/sidesheet/SheetUtils.class", "size": 816, "crc": 831125088}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior$1.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior$1.class", "size": 4334, "crc": -2111961375}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior$2.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior$2.class", "size": 1347, "crc": -1573521381}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior$SavedState$1.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior$SavedState$1.class", "size": 2191, "crc": 1183639970}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior$SavedState.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior$SavedState.class", "size": 2201, "crc": 1642620913}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior$StateSettlingTracker.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior$StateSettlingTracker.class", "size": 2612, "crc": 1520204167}, {"key": "com/google/android/material/sidesheet/SideSheetBehavior.class", "name": "com/google/android/material/sidesheet/SideSheetBehavior.class", "size": 36326, "crc": 2073669012}, {"key": "com/google/android/material/sidesheet/SideSheetCallback.class", "name": "com/google/android/material/sidesheet/SideSheetCallback.class", "size": 753, "crc": -1388861231}, {"key": "com/google/android/material/sidesheet/SideSheetDialog$1.class", "name": "com/google/android/material/sidesheet/SideSheetDialog$1.class", "size": 1295, "crc": -1690137266}, {"key": "com/google/android/material/sidesheet/SideSheetDialog.class", "name": "com/google/android/material/sidesheet/SideSheetDialog.class", "size": 5200, "crc": 901227933}, {"key": "com/google/android/material/slider/BaseOnChangeListener.class", "name": "com/google/android/material/slider/BaseOnChangeListener.class", "size": 645, "crc": 1854245277}, {"key": "com/google/android/material/slider/BaseOnSliderTouchListener.class", "name": "com/google/android/material/slider/BaseOnSliderTouchListener.class", "size": 705, "crc": -1753765294}, {"key": "com/google/android/material/slider/BaseSlider$1.class", "name": "com/google/android/material/slider/BaseSlider$1.class", "size": 1655, "crc": -617725518}, {"key": "com/google/android/material/slider/BaseSlider$AccessibilityEventSender.class", "name": "com/google/android/material/slider/BaseSlider$AccessibilityEventSender.class", "size": 1745, "crc": -1191638446}, {"key": "com/google/android/material/slider/BaseSlider$AccessibilityHelper.class", "name": "com/google/android/material/slider/BaseSlider$AccessibilityHelper.class", "size": 7676, "crc": -389556168}, {"key": "com/google/android/material/slider/BaseSlider$FullCornerDirection.class", "name": "com/google/android/material/slider/BaseSlider$FullCornerDirection.class", "size": 1517, "crc": 447995376}, {"key": "com/google/android/material/slider/BaseSlider$Orientation.class", "name": "com/google/android/material/slider/BaseSlider$Orientation.class", "size": 423, "crc": -1258732239}, {"key": "com/google/android/material/slider/BaseSlider$SliderState$1.class", "name": "com/google/android/material/slider/BaseSlider$SliderState$1.class", "size": 1703, "crc": 729534333}, {"key": "com/google/android/material/slider/BaseSlider$SliderState.class", "name": "com/google/android/material/slider/BaseSlider$SliderState.class", "size": 2479, "crc": 1857285683}, {"key": "com/google/android/material/slider/BaseSlider.class", "name": "com/google/android/material/slider/BaseSlider.class", "size": 84961, "crc": -972109122}, {"key": "com/google/android/material/slider/BasicLabelFormatter.class", "name": "com/google/android/material/slider/BasicLabelFormatter.class", "size": 1274, "crc": 458402003}, {"key": "com/google/android/material/slider/LabelFormatter.class", "name": "com/google/android/material/slider/LabelFormatter.class", "size": 442, "crc": 208090157}, {"key": "com/google/android/material/slider/RangeSlider$1.class", "name": "com/google/android/material/slider/RangeSlider$1.class", "size": 251, "crc": 653843898}, {"key": "com/google/android/material/slider/RangeSlider$OnChangeListener.class", "name": "com/google/android/material/slider/RangeSlider$OnChangeListener.class", "size": 917, "crc": -1471517822}, {"key": "com/google/android/material/slider/RangeSlider$OnSliderTouchListener.class", "name": "com/google/android/material/slider/RangeSlider$OnSliderTouchListener.class", "size": 1071, "crc": -1963225636}, {"key": "com/google/android/material/slider/RangeSlider$RangeSliderState$1.class", "name": "com/google/android/material/slider/RangeSlider$RangeSliderState$1.class", "size": 1569, "crc": 305757159}, {"key": "com/google/android/material/slider/RangeSlider$RangeSliderState.class", "name": "com/google/android/material/slider/RangeSlider$RangeSliderState.class", "size": 2570, "crc": -955186096}, {"key": "com/google/android/material/slider/RangeSlider.class", "name": "com/google/android/material/slider/RangeSlider.class", "size": 18460, "crc": -79923642}, {"key": "com/google/android/material/slider/Slider$OnChangeListener.class", "name": "com/google/android/material/slider/Slider$OnChangeListener.class", "size": 887, "crc": 204274936}, {"key": "com/google/android/material/slider/Slider$OnSliderTouchListener.class", "name": "com/google/android/material/slider/Slider$OnSliderTouchListener.class", "size": 1041, "crc": 1429184691}, {"key": "com/google/android/material/slider/Slider.class", "name": "com/google/android/material/slider/Slider.class", "size": 15397, "crc": -774701449}, {"key": "com/google/android/material/slider/SliderOrientation.class", "name": "com/google/android/material/slider/SliderOrientation.class", "size": 269, "crc": -552591652}, {"key": "com/google/android/material/slider/TickVisibilityMode.class", "name": "com/google/android/material/slider/TickVisibilityMode.class", "size": 496, "crc": 1018331253}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$1.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$1.class", "size": 1053, "crc": 1659415516}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$10.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$10.class", "size": 1064, "crc": -1958746953}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$11.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$11.class", "size": 1584, "crc": -647457860}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$12.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$12.class", "size": 1650, "crc": -1852162998}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$13.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$13.class", "size": 1524, "crc": 338154787}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$14.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$14.class", "size": 1598, "crc": -993662265}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$15.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$15.class", "size": 1532, "crc": 1585598649}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$16.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$16.class", "size": 1606, "crc": -1814792816}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$2.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$2.class", "size": 2565, "crc": -1260451040}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$3.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$3.class", "size": 1737, "crc": -935858486}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$4.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$4.class", "size": 1794, "crc": 334159297}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$5.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$5.class", "size": 1378, "crc": -728902073}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$6.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$6.class", "size": 1019, "crc": 1302279162}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$7.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$7.class", "size": 2345, "crc": 568322958}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$8.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$8.class", "size": 1393, "crc": -491171583}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$9.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$9.class", "size": 1005, "crc": -673275887}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$Anchor.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$Anchor.class", "size": 3278, "crc": -551571748}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$AnimationMode.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$AnimationMode.class", "size": 705, "crc": 424520900}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$BaseCallback$DismissEvent.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$BaseCallback$DismissEvent.class", "size": 817, "crc": -1687185059}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$BaseCallback.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$BaseCallback.class", "size": 1413, "crc": -1158246616}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$Behavior.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$Behavior.class", "size": 2422, "crc": 2062089850}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$BehaviorDelegate.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$BehaviorDelegate.class", "size": 3267, "crc": 1530631349}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$ContentViewCallback.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$ContentViewCallback.class", "size": 448, "crc": -624573716}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$Duration.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$Duration.class", "size": 753, "crc": -1253046310}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$SnackbarBaseLayout$1.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$SnackbarBaseLayout$1.class", "size": 1059, "crc": -93292614}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar$SnackbarBaseLayout.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar$SnackbarBaseLayout.class", "size": 10377, "crc": 111767325}, {"key": "com/google/android/material/snackbar/BaseTransientBottomBar.class", "name": "com/google/android/material/snackbar/BaseTransientBottomBar.class", "size": 28840, "crc": -1998967260}, {"key": "com/google/android/material/snackbar/ContentViewCallback.class", "name": "com/google/android/material/snackbar/ContentViewCallback.class", "size": 219, "crc": -454547442}, {"key": "com/google/android/material/snackbar/Snackbar$Callback.class", "name": "com/google/android/material/snackbar/Snackbar$Callback.class", "size": 1573, "crc": -41602144}, {"key": "com/google/android/material/snackbar/Snackbar$SnackbarLayout.class", "name": "com/google/android/material/snackbar/Snackbar$SnackbarLayout.class", "size": 3196, "crc": 20068239}, {"key": "com/google/android/material/snackbar/Snackbar.class", "name": "com/google/android/material/snackbar/Snackbar.class", "size": 11895, "crc": 538074144}, {"key": "com/google/android/material/snackbar/SnackbarContentLayout.class", "name": "com/google/android/material/snackbar/SnackbarContentLayout.class", "size": 5828, "crc": 803012001}, {"key": "com/google/android/material/snackbar/SnackbarManager$1.class", "name": "com/google/android/material/snackbar/SnackbarManager$1.class", "size": 1318, "crc": 1984982961}, {"key": "com/google/android/material/snackbar/SnackbarManager$Callback.class", "name": "com/google/android/material/snackbar/SnackbarManager$Callback.class", "size": 303, "crc": 999759441}, {"key": "com/google/android/material/snackbar/SnackbarManager$SnackbarRecord.class", "name": "com/google/android/material/snackbar/SnackbarManager$SnackbarRecord.class", "size": 1444, "crc": -1223207518}, {"key": "com/google/android/material/snackbar/SnackbarManager.class", "name": "com/google/android/material/snackbar/SnackbarManager.class", "size": 5515, "crc": 1136379928}, {"key": "com/google/android/material/stateful/ExtendableSavedState$1.class", "name": "com/google/android/material/stateful/ExtendableSavedState$1.class", "size": 2112, "crc": 1148154522}, {"key": "com/google/android/material/stateful/ExtendableSavedState.class", "name": "com/google/android/material/stateful/ExtendableSavedState.class", "size": 3435, "crc": 571407917}, {"key": "com/google/android/material/switchmaterial/SwitchMaterial.class", "name": "com/google/android/material/switchmaterial/SwitchMaterial.class", "size": 4969, "crc": 1567290495}, {"key": "com/google/android/material/tabs/ElasticTabIndicatorInterpolator.class", "name": "com/google/android/material/tabs/ElasticTabIndicatorInterpolator.class", "size": 2314, "crc": -1907034777}, {"key": "com/google/android/material/tabs/FadeTabIndicatorInterpolator.class", "name": "com/google/android/material/tabs/FadeTabIndicatorInterpolator.class", "size": 1777, "crc": -218612126}, {"key": "com/google/android/material/tabs/TabIndicatorInterpolator.class", "name": "com/google/android/material/tabs/TabIndicatorInterpolator.class", "size": 3569, "crc": -1322007754}, {"key": "com/google/android/material/tabs/TabItem.class", "name": "com/google/android/material/tabs/TabItem.class", "size": 1470, "crc": -9272832}, {"key": "com/google/android/material/tabs/TabLayout$1.class", "name": "com/google/android/material/tabs/TabLayout$1.class", "size": 1168, "crc": -361298061}, {"key": "com/google/android/material/tabs/TabLayout$AdapterChangeListener.class", "name": "com/google/android/material/tabs/TabLayout$AdapterChangeListener.class", "size": 1522, "crc": 1464922040}, {"key": "com/google/android/material/tabs/TabLayout$BaseOnTabSelectedListener.class", "name": "com/google/android/material/tabs/TabLayout$BaseOnTabSelectedListener.class", "size": 671, "crc": 41829030}, {"key": "com/google/android/material/tabs/TabLayout$LabelVisibility.class", "name": "com/google/android/material/tabs/TabLayout$LabelVisibility.class", "size": 424, "crc": 1349846162}, {"key": "com/google/android/material/tabs/TabLayout$Mode.class", "name": "com/google/android/material/tabs/TabLayout$Mode.class", "size": 640, "crc": -1083765469}, {"key": "com/google/android/material/tabs/TabLayout$OnTabSelectedListener.class", "name": "com/google/android/material/tabs/TabLayout$OnTabSelectedListener.class", "size": 596, "crc": -104311863}, {"key": "com/google/android/material/tabs/TabLayout$PagerAdapterObserver.class", "name": "com/google/android/material/tabs/TabLayout$PagerAdapterObserver.class", "size": 838, "crc": -815765008}, {"key": "com/google/android/material/tabs/TabLayout$SlidingTabIndicator$1.class", "name": "com/google/android/material/tabs/TabLayout$SlidingTabIndicator$1.class", "size": 1584, "crc": -129437546}, {"key": "com/google/android/material/tabs/TabLayout$SlidingTabIndicator.class", "name": "com/google/android/material/tabs/TabLayout$SlidingTabIndicator.class", "size": 8457, "crc": -563102708}, {"key": "com/google/android/material/tabs/TabLayout$Tab.class", "name": "com/google/android/material/tabs/TabLayout$Tab.class", "size": 6977, "crc": 906527948}, {"key": "com/google/android/material/tabs/TabLayout$TabGravity.class", "name": "com/google/android/material/tabs/TabLayout$TabGravity.class", "size": 652, "crc": 1587177330}, {"key": "com/google/android/material/tabs/TabLayout$TabIndicatorAnimationMode.class", "name": "com/google/android/material/tabs/TabLayout$TabIndicatorAnimationMode.class", "size": 682, "crc": -1828597154}, {"key": "com/google/android/material/tabs/TabLayout$TabIndicatorGravity.class", "name": "com/google/android/material/tabs/TabLayout$TabIndicatorGravity.class", "size": 670, "crc": 1780961856}, {"key": "com/google/android/material/tabs/TabLayout$TabLayoutOnPageChangeListener.class", "name": "com/google/android/material/tabs/TabLayout$TabLayoutOnPageChangeListener.class", "size": 2410, "crc": 770282870}, {"key": "com/google/android/material/tabs/TabLayout$TabView$1.class", "name": "com/google/android/material/tabs/TabLayout$TabView$1.class", "size": 1352, "crc": -1793580955}, {"key": "com/google/android/material/tabs/TabLayout$TabView.class", "name": "com/google/android/material/tabs/TabLayout$TabView.class", "size": 19180, "crc": -1379490020}, {"key": "com/google/android/material/tabs/TabLayout$ViewPagerOnTabSelectedListener.class", "name": "com/google/android/material/tabs/TabLayout$ViewPagerOnTabSelectedListener.class", "size": 1366, "crc": -812144781}, {"key": "com/google/android/material/tabs/TabLayout.class", "name": "com/google/android/material/tabs/TabLayout.class", "size": 42768, "crc": 2138432893}, {"key": "com/google/android/material/tabs/TabLayoutMediator$PagerAdapterObserver.class", "name": "com/google/android/material/tabs/TabLayoutMediator$PagerAdapterObserver.class", "size": 1678, "crc": -269966116}, {"key": "com/google/android/material/tabs/TabLayoutMediator$TabConfigurationStrategy.class", "name": "com/google/android/material/tabs/TabLayoutMediator$TabConfigurationStrategy.class", "size": 567, "crc": 1552938396}, {"key": "com/google/android/material/tabs/TabLayoutMediator$TabLayoutOnPageChangeCallback.class", "name": "com/google/android/material/tabs/TabLayoutMediator$TabLayoutOnPageChangeCallback.class", "size": 2488, "crc": 22829252}, {"key": "com/google/android/material/tabs/TabLayoutMediator$ViewPagerOnTabSelectedListener.class", "name": "com/google/android/material/tabs/TabLayoutMediator$ViewPagerOnTabSelectedListener.class", "size": 1514, "crc": -333535114}, {"key": "com/google/android/material/tabs/TabLayoutMediator.class", "name": "com/google/android/material/tabs/TabLayoutMediator.class", "size": 5932, "crc": 634192056}, {"key": "com/google/android/material/textfield/ClearTextEndIconDelegate$1.class", "name": "com/google/android/material/textfield/ClearTextEndIconDelegate$1.class", "size": 1117, "crc": 1117666401}, {"key": "com/google/android/material/textfield/ClearTextEndIconDelegate$2.class", "name": "com/google/android/material/textfield/ClearTextEndIconDelegate$2.class", "size": 1115, "crc": 390107028}, {"key": "com/google/android/material/textfield/ClearTextEndIconDelegate.class", "name": "com/google/android/material/textfield/ClearTextEndIconDelegate.class", "size": 8778, "crc": -1086636886}, {"key": "com/google/android/material/textfield/CustomEndIconDelegate.class", "name": "com/google/android/material/textfield/CustomEndIconDelegate.class", "size": 989, "crc": -117097821}, {"key": "com/google/android/material/textfield/CutoutDrawable$1.class", "name": "com/google/android/material/textfield/CutoutDrawable$1.class", "size": 266, "crc": -1081654528}, {"key": "com/google/android/material/textfield/CutoutDrawable$CutoutDrawableState.class", "name": "com/google/android/material/textfield/CutoutDrawable$CutoutDrawableState.class", "size": 2663, "crc": 91596837}, {"key": "com/google/android/material/textfield/CutoutDrawable$ImplApi18.class", "name": "com/google/android/material/textfield/CutoutDrawable$ImplApi18.class", "size": 2013, "crc": 1062247291}, {"key": "com/google/android/material/textfield/CutoutDrawable.class", "name": "com/google/android/material/textfield/CutoutDrawable.class", "size": 3500, "crc": 2112036352}, {"key": "com/google/android/material/textfield/DropdownMenuEndIconDelegate$1.class", "name": "com/google/android/material/textfield/DropdownMenuEndIconDelegate$1.class", "size": 1175, "crc": -967624189}, {"key": "com/google/android/material/textfield/DropdownMenuEndIconDelegate.class", "name": "com/google/android/material/textfield/DropdownMenuEndIconDelegate.class", "size": 13386, "crc": 1599300126}, {"key": "com/google/android/material/textfield/EditTextUtils.class", "name": "com/google/android/material/textfield/EditTextUtils.class", "size": 663, "crc": 157290520}, {"key": "com/google/android/material/textfield/EndCompoundLayout$1.class", "name": "com/google/android/material/textfield/EndCompoundLayout$1.class", "size": 1311, "crc": 1479521546}, {"key": "com/google/android/material/textfield/EndCompoundLayout$2.class", "name": "com/google/android/material/textfield/EndCompoundLayout$2.class", "size": 2573, "crc": 845929693}, {"key": "com/google/android/material/textfield/EndCompoundLayout$3.class", "name": "com/google/android/material/textfield/EndCompoundLayout$3.class", "size": 1204, "crc": 2103427238}, {"key": "com/google/android/material/textfield/EndCompoundLayout$EndIconDelegates.class", "name": "com/google/android/material/textfield/EndCompoundLayout$EndIconDelegates.class", "size": 3081, "crc": 822815031}, {"key": "com/google/android/material/textfield/EndCompoundLayout.class", "name": "com/google/android/material/textfield/EndCompoundLayout.class", "size": 28220, "crc": 1219671202}, {"key": "com/google/android/material/textfield/EndIconDelegate.class", "name": "com/google/android/material/textfield/EndIconDelegate.class", "size": 4312, "crc": -509191952}, {"key": "com/google/android/material/textfield/IconHelper.class", "name": "com/google/android/material/textfield/IconHelper.class", "size": 6490, "crc": -1949397608}, {"key": "com/google/android/material/textfield/IndicatorViewController$1.class", "name": "com/google/android/material/textfield/IndicatorViewController$1.class", "size": 2048, "crc": -1290251414}, {"key": "com/google/android/material/textfield/IndicatorViewController$2.class", "name": "com/google/android/material/textfield/IndicatorViewController$2.class", "size": 1580, "crc": -450044925}, {"key": "com/google/android/material/textfield/IndicatorViewController.class", "name": "com/google/android/material/textfield/IndicatorViewController.class", "size": 17655, "crc": 1148687808}, {"key": "com/google/android/material/textfield/MaterialAutoCompleteTextView$1.class", "name": "com/google/android/material/textfield/MaterialAutoCompleteTextView$1.class", "size": 2556, "crc": -270277774}, {"key": "com/google/android/material/textfield/MaterialAutoCompleteTextView$MaterialArrayAdapter.class", "name": "com/google/android/material/textfield/MaterialAutoCompleteTextView$MaterialArrayAdapter.class", "size": 4888, "crc": 1218825404}, {"key": "com/google/android/material/textfield/MaterialAutoCompleteTextView.class", "name": "com/google/android/material/textfield/MaterialAutoCompleteTextView.class", "size": 14404, "crc": -1907227249}, {"key": "com/google/android/material/textfield/NoEndIconDelegate.class", "name": "com/google/android/material/textfield/NoEndIconDelegate.class", "size": 617, "crc": -1155314876}, {"key": "com/google/android/material/textfield/PasswordToggleEndIconDelegate.class", "name": "com/google/android/material/textfield/PasswordToggleEndIconDelegate.class", "size": 4276, "crc": -1224081648}, {"key": "com/google/android/material/textfield/StartCompoundLayout.class", "name": "com/google/android/material/textfield/StartCompoundLayout.class", "size": 13159, "crc": -1330041390}, {"key": "com/google/android/material/textfield/TextInputEditText.class", "name": "com/google/android/material/textfield/TextInputEditText.class", "size": 6991, "crc": -1033601873}, {"key": "com/google/android/material/textfield/TextInputLayout$1.class", "name": "com/google/android/material/textfield/TextInputLayout$1.class", "size": 2209, "crc": -984346071}, {"key": "com/google/android/material/textfield/TextInputLayout$2.class", "name": "com/google/android/material/textfield/TextInputLayout$2.class", "size": 1267, "crc": -395488254}, {"key": "com/google/android/material/textfield/TextInputLayout$3.class", "name": "com/google/android/material/textfield/TextInputLayout$3.class", "size": 1047, "crc": 419504877}, {"key": "com/google/android/material/textfield/TextInputLayout$4.class", "name": "com/google/android/material/textfield/TextInputLayout$4.class", "size": 1406, "crc": -2146193901}, {"key": "com/google/android/material/textfield/TextInputLayout$AccessibilityDelegate.class", "name": "com/google/android/material/textfield/TextInputLayout$AccessibilityDelegate.class", "size": 4832, "crc": 2008375602}, {"key": "com/google/android/material/textfield/TextInputLayout$BoxBackgroundMode.class", "name": "com/google/android/material/textfield/TextInputLayout$BoxBackgroundMode.class", "size": 456, "crc": -1542205345}, {"key": "com/google/android/material/textfield/TextInputLayout$EndIconMode.class", "name": "com/google/android/material/textfield/TextInputLayout$EndIconMode.class", "size": 682, "crc": 589432917}, {"key": "com/google/android/material/textfield/TextInputLayout$LengthCounter.class", "name": "com/google/android/material/textfield/TextInputLayout$LengthCounter.class", "size": 405, "crc": 1112915766}, {"key": "com/google/android/material/textfield/TextInputLayout$OnEditTextAttachedListener.class", "name": "com/google/android/material/textfield/TextInputLayout$OnEditTextAttachedListener.class", "size": 469, "crc": 644291782}, {"key": "com/google/android/material/textfield/TextInputLayout$OnEndIconChangedListener.class", "name": "com/google/android/material/textfield/TextInputLayout$OnEndIconChangedListener.class", "size": 466, "crc": 1126521787}, {"key": "com/google/android/material/textfield/TextInputLayout$SavedState$1.class", "name": "com/google/android/material/textfield/TextInputLayout$SavedState$1.class", "size": 2173, "crc": 27007648}, {"key": "com/google/android/material/textfield/TextInputLayout$SavedState.class", "name": "com/google/android/material/textfield/TextInputLayout$SavedState.class", "size": 2642, "crc": -788914545}, {"key": "com/google/android/material/textfield/TextInputLayout.class", "name": "com/google/android/material/textfield/TextInputLayout.class", "size": 86548, "crc": 1303951823}, {"key": "com/google/android/material/textview/MaterialTextView.class", "name": "com/google/android/material/textview/MaterialTextView.class", "size": 4948, "crc": 1030826309}, {"key": "com/google/android/material/theme/MaterialComponentsViewInflater.class", "name": "com/google/android/material/theme/MaterialComponentsViewInflater.class", "size": 2168, "crc": 1127883017}, {"key": "com/google/android/material/theme/overlay/MaterialThemeOverlay.class", "name": "com/google/android/material/theme/overlay/MaterialThemeOverlay.class", "size": 3760, "crc": -1441012180}, {"key": "com/google/android/material/timepicker/ChipTextInputComboView$1.class", "name": "com/google/android/material/timepicker/ChipTextInputComboView$1.class", "size": 292, "crc": 494236582}, {"key": "com/google/android/material/timepicker/ChipTextInputComboView$TextFormatter.class", "name": "com/google/android/material/timepicker/ChipTextInputComboView$TextFormatter.class", "size": 2045, "crc": -1745755348}, {"key": "com/google/android/material/timepicker/ChipTextInputComboView.class", "name": "com/google/android/material/timepicker/ChipTextInputComboView.class", "size": 7594, "crc": 1395623007}, {"key": "com/google/android/material/timepicker/ClickActionDelegate.class", "name": "com/google/android/material/timepicker/ClickActionDelegate.class", "size": 1427, "crc": 1922881674}, {"key": "com/google/android/material/timepicker/ClockFaceView$1.class", "name": "com/google/android/material/timepicker/ClockFaceView$1.class", "size": 1591, "crc": 1808320040}, {"key": "com/google/android/material/timepicker/ClockFaceView$2.class", "name": "com/google/android/material/timepicker/ClockFaceView$2.class", "size": 3558, "crc": -2094052821}, {"key": "com/google/android/material/timepicker/ClockFaceView.class", "name": "com/google/android/material/timepicker/ClockFaceView.class", "size": 13425, "crc": 373398587}, {"key": "com/google/android/material/timepicker/ClockHandView$1.class", "name": "com/google/android/material/timepicker/ClockHandView$1.class", "size": 925, "crc": 255756508}, {"key": "com/google/android/material/timepicker/ClockHandView$OnActionUpListener.class", "name": "com/google/android/material/timepicker/ClockHandView$OnActionUpListener.class", "size": 433, "crc": -831194912}, {"key": "com/google/android/material/timepicker/ClockHandView$OnRotateListener.class", "name": "com/google/android/material/timepicker/ClockHandView$OnRotateListener.class", "size": 427, "crc": 325092054}, {"key": "com/google/android/material/timepicker/ClockHandView.class", "name": "com/google/android/material/timepicker/ClockHandView.class", "size": 13291, "crc": -488242791}, {"key": "com/google/android/material/timepicker/MaterialTimePicker$1.class", "name": "com/google/android/material/timepicker/MaterialTimePicker$1.class", "size": 1460, "crc": 730616517}, {"key": "com/google/android/material/timepicker/MaterialTimePicker$2.class", "name": "com/google/android/material/timepicker/MaterialTimePicker$2.class", "size": 1461, "crc": 2077929727}, {"key": "com/google/android/material/timepicker/MaterialTimePicker$3.class", "name": "com/google/android/material/timepicker/MaterialTimePicker$3.class", "size": 1564, "crc": 1512966430}, {"key": "com/google/android/material/timepicker/MaterialTimePicker$Builder.class", "name": "com/google/android/material/timepicker/MaterialTimePicker$Builder.class", "size": 4484, "crc": -780730365}, {"key": "com/google/android/material/timepicker/MaterialTimePicker.class", "name": "com/google/android/material/timepicker/MaterialTimePicker.class", "size": 20204, "crc": 2071971351}, {"key": "com/google/android/material/timepicker/MaxInputValidator.class", "name": "com/google/android/material/timepicker/MaxInputValidator.class", "size": 1532, "crc": 405726350}, {"key": "com/google/android/material/timepicker/RadialViewGroup.class", "name": "com/google/android/material/timepicker/RadialViewGroup.class", "size": 7762, "crc": -1574888306}, {"key": "com/google/android/material/timepicker/TimeFormat.class", "name": "com/google/android/material/timepicker/TimeFormat.class", "size": 405, "crc": 1505144742}, {"key": "com/google/android/material/timepicker/TimeModel$1.class", "name": "com/google/android/material/timepicker/TimeModel$1.class", "size": 1285, "crc": -1038619511}, {"key": "com/google/android/material/timepicker/TimeModel.class", "name": "com/google/android/material/timepicker/TimeModel.class", "size": 5267, "crc": 1360434074}, {"key": "com/google/android/material/timepicker/TimePickerClockPresenter$1.class", "name": "com/google/android/material/timepicker/TimePickerClockPresenter$1.class", "size": 1984, "crc": 145756293}, {"key": "com/google/android/material/timepicker/TimePickerClockPresenter$2.class", "name": "com/google/android/material/timepicker/TimePickerClockPresenter$2.class", "size": 2038, "crc": -1372105477}, {"key": "com/google/android/material/timepicker/TimePickerClockPresenter.class", "name": "com/google/android/material/timepicker/TimePickerClockPresenter.class", "size": 8212, "crc": -1647904070}, {"key": "com/google/android/material/timepicker/TimePickerControls$ActiveSelection.class", "name": "com/google/android/material/timepicker/TimePickerControls$ActiveSelection.class", "size": 463, "crc": -1834295698}, {"key": "com/google/android/material/timepicker/TimePickerControls$ClockPeriod.class", "name": "com/google/android/material/timepicker/TimePickerControls$ClockPeriod.class", "size": 455, "crc": -1022076531}, {"key": "com/google/android/material/timepicker/TimePickerControls.class", "name": "com/google/android/material/timepicker/TimePickerControls.class", "size": 685, "crc": -192125558}, {"key": "com/google/android/material/timepicker/TimePickerPresenter.class", "name": "com/google/android/material/timepicker/TimePickerPresenter.class", "size": 236, "crc": -1773721679}, {"key": "com/google/android/material/timepicker/TimePickerTextInputKeyController.class", "name": "com/google/android/material/timepicker/TimePickerTextInputKeyController.class", "size": 4091, "crc": -442060111}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$1.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$1.class", "size": 1551, "crc": -181679260}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$2.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$2.class", "size": 1547, "crc": -895839480}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$3.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$3.class", "size": 1322, "crc": 1514339191}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$4.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$4.class", "size": 2505, "crc": 169645865}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$5.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$5.class", "size": 2470, "crc": -1106987181}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter$6.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter$6.class", "size": 1596, "crc": 2050465752}, {"key": "com/google/android/material/timepicker/TimePickerTextInputPresenter.class", "name": "com/google/android/material/timepicker/TimePickerTextInputPresenter.class", "size": 9723, "crc": -1530698514}, {"key": "com/google/android/material/timepicker/TimePickerView$1.class", "name": "com/google/android/material/timepicker/TimePickerView$1.class", "size": 1453, "crc": -1792288301}, {"key": "com/google/android/material/timepicker/TimePickerView$2.class", "name": "com/google/android/material/timepicker/TimePickerView$2.class", "size": 1373, "crc": -1575963189}, {"key": "com/google/android/material/timepicker/TimePickerView$3.class", "name": "com/google/android/material/timepicker/TimePickerView$3.class", "size": 1307, "crc": 136401509}, {"key": "com/google/android/material/timepicker/TimePickerView$OnDoubleTapListener.class", "name": "com/google/android/material/timepicker/TimePickerView$OnDoubleTapListener.class", "size": 308, "crc": -1620642059}, {"key": "com/google/android/material/timepicker/TimePickerView$OnPeriodChangeListener.class", "name": "com/google/android/material/timepicker/TimePickerView$OnPeriodChangeListener.class", "size": 318, "crc": 74087755}, {"key": "com/google/android/material/timepicker/TimePickerView$OnSelectionChange.class", "name": "com/google/android/material/timepicker/TimePickerView$OnSelectionChange.class", "size": 312, "crc": -634433523}, {"key": "com/google/android/material/timepicker/TimePickerView.class", "name": "com/google/android/material/timepicker/TimePickerView.class", "size": 11463, "crc": -1247587257}, {"key": "com/google/android/material/tooltip/TooltipDrawable$1.class", "name": "com/google/android/material/tooltip/TooltipDrawable$1.class", "size": 1165, "crc": -2128522954}, {"key": "com/google/android/material/tooltip/TooltipDrawable.class", "name": "com/google/android/material/tooltip/TooltipDrawable.class", "size": 14426, "crc": 624740751}, {"key": "com/google/android/material/transformation/ExpandableBehavior$1.class", "name": "com/google/android/material/transformation/ExpandableBehavior$1.class", "size": 1832, "crc": -1768940248}, {"key": "com/google/android/material/transformation/ExpandableBehavior.class", "name": "com/google/android/material/transformation/ExpandableBehavior.class", "size": 5416, "crc": -932431244}, {"key": "com/google/android/material/transformation/ExpandableTransformationBehavior$1.class", "name": "com/google/android/material/transformation/ExpandableTransformationBehavior$1.class", "size": 1227, "crc": 1436088306}, {"key": "com/google/android/material/transformation/ExpandableTransformationBehavior.class", "name": "com/google/android/material/transformation/ExpandableTransformationBehavior.class", "size": 2263, "crc": -1567238994}, {"key": "com/google/android/material/transformation/FabTransformationBehavior$1.class", "name": "com/google/android/material/transformation/FabTransformationBehavior$1.class", "size": 1539, "crc": 1464257807}, {"key": "com/google/android/material/transformation/FabTransformationBehavior$2.class", "name": "com/google/android/material/transformation/FabTransformationBehavior$2.class", "size": 1536, "crc": 579669276}, {"key": "com/google/android/material/transformation/FabTransformationBehavior$3.class", "name": "com/google/android/material/transformation/FabTransformationBehavior$3.class", "size": 1831, "crc": 42942667}, {"key": "com/google/android/material/transformation/FabTransformationBehavior$4.class", "name": "com/google/android/material/transformation/FabTransformationBehavior$4.class", "size": 2001, "crc": -1337989116}, {"key": "com/google/android/material/transformation/FabTransformationBehavior$FabTransformationSpec.class", "name": "com/google/android/material/transformation/FabTransformationBehavior$FabTransformationSpec.class", "size": 781, "crc": 353376486}, {"key": "com/google/android/material/transformation/FabTransformationBehavior.class", "name": "com/google/android/material/transformation/FabTransformationBehavior.class", "size": 23214, "crc": 1371210975}, {"key": "com/google/android/material/transformation/FabTransformationScrimBehavior$1.class", "name": "com/google/android/material/transformation/FabTransformationScrimBehavior$1.class", "size": 1429, "crc": -2080222187}, {"key": "com/google/android/material/transformation/FabTransformationScrimBehavior.class", "name": "com/google/android/material/transformation/FabTransformationScrimBehavior.class", "size": 4300, "crc": 1492425135}, {"key": "com/google/android/material/transformation/FabTransformationSheetBehavior.class", "name": "com/google/android/material/transformation/FabTransformationSheetBehavior.class", "size": 4515, "crc": 1656097808}, {"key": "com/google/android/material/transformation/TransformationChildCard.class", "name": "com/google/android/material/transformation/TransformationChildCard.class", "size": 789, "crc": -1227038093}, {"key": "com/google/android/material/transformation/TransformationChildLayout.class", "name": "com/google/android/material/transformation/TransformationChildLayout.class", "size": 925, "crc": -249456540}, {"key": "com/google/android/material/transition/FadeModeEvaluator.class", "name": "com/google/android/material/transition/FadeModeEvaluator.class", "size": 237, "crc": 1420380706}, {"key": "com/google/android/material/transition/FadeModeEvaluators$1.class", "name": "com/google/android/material/transition/FadeModeEvaluators$1.class", "size": 1094, "crc": -173916448}, {"key": "com/google/android/material/transition/FadeModeEvaluators$2.class", "name": "com/google/android/material/transition/FadeModeEvaluators$2.class", "size": 1096, "crc": -908736983}, {"key": "com/google/android/material/transition/FadeModeEvaluators$3.class", "name": "com/google/android/material/transition/FadeModeEvaluators$3.class", "size": 1103, "crc": -139286947}, {"key": "com/google/android/material/transition/FadeModeEvaluators$4.class", "name": "com/google/android/material/transition/FadeModeEvaluators$4.class", "size": 1190, "crc": 189550818}, {"key": "com/google/android/material/transition/FadeModeEvaluators.class", "name": "com/google/android/material/transition/FadeModeEvaluators.class", "size": 1695, "crc": **********}, {"key": "com/google/android/material/transition/FadeModeResult.class", "name": "com/google/android/material/transition/FadeModeResult.class", "size": 751, "crc": 635682811}, {"key": "com/google/android/material/transition/FadeProvider$1.class", "name": "com/google/android/material/transition/FadeProvider$1.class", "size": 1429, "crc": **********}, {"key": "com/google/android/material/transition/FadeProvider$2.class", "name": "com/google/android/material/transition/FadeProvider$2.class", "size": 923, "crc": **********}, {"key": "com/google/android/material/transition/FadeProvider.class", "name": "com/google/android/material/transition/FadeProvider.class", "size": 2556, "crc": **********}, {"key": "com/google/android/material/transition/FadeThroughProvider$1.class", "name": "com/google/android/material/transition/FadeThroughProvider$1.class", "size": 1464, "crc": **********}, {"key": "com/google/android/material/transition/FadeThroughProvider$2.class", "name": "com/google/android/material/transition/FadeThroughProvider$2.class", "size": 958, "crc": **********}, {"key": "com/google/android/material/transition/FadeThroughProvider.class", "name": "com/google/android/material/transition/FadeThroughProvider.class", "size": 2678, "crc": -**********}, {"key": "com/google/android/material/transition/FitModeEvaluator.class", "name": "com/google/android/material/transition/FitModeEvaluator.class", "size": 434, "crc": -**********}, {"key": "com/google/android/material/transition/FitModeEvaluators$1.class", "name": "com/google/android/material/transition/FitModeEvaluators$1.class", "size": 1921, "crc": 206441746}, {"key": "com/google/android/material/transition/FitModeEvaluators$2.class", "name": "com/google/android/material/transition/FitModeEvaluators$2.class", "size": 1956, "crc": -593004090}, {"key": "com/google/android/material/transition/FitModeEvaluators.class", "name": "com/google/android/material/transition/FitModeEvaluators.class", "size": 2077, "crc": **********}, {"key": "com/google/android/material/transition/FitModeResult.class", "name": "com/google/android/material/transition/FitModeResult.class", "size": 689, "crc": -147181480}, {"key": "com/google/android/material/transition/Hold.class", "name": "com/google/android/material/transition/Hold.class", "size": 1172, "crc": 1629436232}, {"key": "com/google/android/material/transition/MaskEvaluator.class", "name": "com/google/android/material/transition/MaskEvaluator.class", "size": 3575, "crc": -167339008}, {"key": "com/google/android/material/transition/MaterialArcMotion.class", "name": "com/google/android/material/transition/MaterialArcMotion.class", "size": 1137, "crc": -1215583414}, {"key": "com/google/android/material/transition/MaterialContainerTransform$1.class", "name": "com/google/android/material/transition/MaterialContainerTransform$1.class", "size": 1726, "crc": -1717197103}, {"key": "com/google/android/material/transition/MaterialContainerTransform$2.class", "name": "com/google/android/material/transition/MaterialContainerTransform$2.class", "size": 2528, "crc": -1825982847}, {"key": "com/google/android/material/transition/MaterialContainerTransform$FadeMode.class", "name": "com/google/android/material/transition/MaterialContainerTransform$FadeMode.class", "size": 711, "crc": 1741671266}, {"key": "com/google/android/material/transition/MaterialContainerTransform$FitMode.class", "name": "com/google/android/material/transition/MaterialContainerTransform$FitMode.class", "size": 709, "crc": -1394112876}, {"key": "com/google/android/material/transition/MaterialContainerTransform$ProgressThresholds.class", "name": "com/google/android/material/transition/MaterialContainerTransform$ProgressThresholds.class", "size": 1302, "crc": 1605634809}, {"key": "com/google/android/material/transition/MaterialContainerTransform$ProgressThresholdsGroup.class", "name": "com/google/android/material/transition/MaterialContainerTransform$ProgressThresholdsGroup.class", "size": 2688, "crc": -1395854775}, {"key": "com/google/android/material/transition/MaterialContainerTransform$TransitionDirection.class", "name": "com/google/android/material/transition/MaterialContainerTransform$TransitionDirection.class", "size": 733, "crc": 558581710}, {"key": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable$1.class", "name": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable$1.class", "size": 1425, "crc": 1262237382}, {"key": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable$2.class", "name": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable$2.class", "size": 1423, "crc": 685068164}, {"key": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable.class", "name": "com/google/android/material/transition/MaterialContainerTransform$TransitionDrawable.class", "size": 17757, "crc": -1440269615}, {"key": "com/google/android/material/transition/MaterialContainerTransform.class", "name": "com/google/android/material/transition/MaterialContainerTransform.class", "size": 22046, "crc": -880146521}, {"key": "com/google/android/material/transition/MaterialElevationScale.class", "name": "com/google/android/material/transition/MaterialElevationScale.class", "size": 3022, "crc": 435015772}, {"key": "com/google/android/material/transition/MaterialFade.class", "name": "com/google/android/material/transition/MaterialFade.class", "size": 4349, "crc": -1130697644}, {"key": "com/google/android/material/transition/MaterialFadeThrough.class", "name": "com/google/android/material/transition/MaterialFadeThrough.class", "size": 3550, "crc": 295765996}, {"key": "com/google/android/material/transition/MaterialSharedAxis$Axis.class", "name": "com/google/android/material/transition/MaterialSharedAxis$Axis.class", "size": 679, "crc": -890379119}, {"key": "com/google/android/material/transition/MaterialSharedAxis.class", "name": "com/google/android/material/transition/MaterialSharedAxis.class", "size": 4364, "crc": -**********}, {"key": "com/google/android/material/transition/MaterialVisibility.class", "name": "com/google/android/material/transition/MaterialVisibility.class", "size": 6250, "crc": -103379684}, {"key": "com/google/android/material/transition/ScaleProvider$1.class", "name": "com/google/android/material/transition/ScaleProvider$1.class", "size": 1009, "crc": -220656751}, {"key": "com/google/android/material/transition/ScaleProvider.class", "name": "com/google/android/material/transition/ScaleProvider.class", "size": 3720, "crc": -154122940}, {"key": "com/google/android/material/transition/SlideDistanceProvider$1.class", "name": "com/google/android/material/transition/SlideDistanceProvider$1.class", "size": 978, "crc": 46784667}, {"key": "com/google/android/material/transition/SlideDistanceProvider$2.class", "name": "com/google/android/material/transition/SlideDistanceProvider$2.class", "size": 978, "crc": **********}, {"key": "com/google/android/material/transition/SlideDistanceProvider$GravityFlag.class", "name": "com/google/android/material/transition/SlideDistanceProvider$GravityFlag.class", "size": 702, "crc": -**********}, {"key": "com/google/android/material/transition/SlideDistanceProvider.class", "name": "com/google/android/material/transition/SlideDistanceProvider.class", "size": 5468, "crc": -861433950}, {"key": "com/google/android/material/transition/TransitionListenerAdapter.class", "name": "com/google/android/material/transition/TransitionListenerAdapter.class", "size": 1044, "crc": **********}, {"key": "com/google/android/material/transition/TransitionUtils$1.class", "name": "com/google/android/material/transition/TransitionUtils$1.class", "size": 1925, "crc": 989319934}, {"key": "com/google/android/material/transition/TransitionUtils$CornerSizeBinaryOperator.class", "name": "com/google/android/material/transition/TransitionUtils$CornerSizeBinaryOperator.class", "size": 584, "crc": -281402986}, {"key": "com/google/android/material/transition/TransitionUtils.class", "name": "com/google/android/material/transition/TransitionUtils.class", "size": 13912, "crc": 59099114}, {"key": "com/google/android/material/transition/VisibilityAnimatorProvider.class", "name": "com/google/android/material/transition/VisibilityAnimatorProvider.class", "size": 493, "crc": -**********}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluator.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluator.class", "size": 351, "crc": **********}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluators$1.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluators$1.class", "size": 1166, "crc": **********}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluators$2.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluators$2.class", "size": 1168, "crc": -812718480}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluators$3.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluators$3.class", "size": 1175, "crc": -**********}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluators$4.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluators$4.class", "size": 1262, "crc": -552739945}, {"key": "com/google/android/material/transition/platform/FadeModeEvaluators.class", "name": "com/google/android/material/transition/platform/FadeModeEvaluators.class", "size": 1881, "crc": **********}, {"key": "com/google/android/material/transition/platform/FadeModeResult.class", "name": "com/google/android/material/transition/platform/FadeModeResult.class", "size": 874, "crc": -342351936}, {"key": "com/google/android/material/transition/platform/FadeProvider$1.class", "name": "com/google/android/material/transition/platform/FadeProvider$1.class", "size": 1465, "crc": 404146420}, {"key": "com/google/android/material/transition/platform/FadeProvider$2.class", "name": "com/google/android/material/transition/platform/FadeProvider$2.class", "size": 950, "crc": 202685334}, {"key": "com/google/android/material/transition/platform/FadeProvider.class", "name": "com/google/android/material/transition/platform/FadeProvider.class", "size": 2667, "crc": 338384609}, {"key": "com/google/android/material/transition/platform/FadeThroughProvider$1.class", "name": "com/google/android/material/transition/platform/FadeThroughProvider$1.class", "size": 1500, "crc": 575834678}, {"key": "com/google/android/material/transition/platform/FadeThroughProvider$2.class", "name": "com/google/android/material/transition/platform/FadeThroughProvider$2.class", "size": 985, "crc": -**********}, {"key": "com/google/android/material/transition/platform/FadeThroughProvider.class", "name": "com/google/android/material/transition/platform/FadeThroughProvider.class", "size": 2789, "crc": -139278281}, {"key": "com/google/android/material/transition/platform/FitModeEvaluator.class", "name": "com/google/android/material/transition/platform/FitModeEvaluator.class", "size": 566, "crc": **********}, {"key": "com/google/android/material/transition/platform/FitModeEvaluators$1.class", "name": "com/google/android/material/transition/platform/FitModeEvaluators$1.class", "size": 2011, "crc": 888477672}, {"key": "com/google/android/material/transition/platform/FitModeEvaluators$2.class", "name": "com/google/android/material/transition/platform/FitModeEvaluators$2.class", "size": 2046, "crc": 264514581}, {"key": "com/google/android/material/transition/platform/FitModeEvaluators.class", "name": "com/google/android/material/transition/platform/FitModeEvaluators.class", "size": 2245, "crc": **********}, {"key": "com/google/android/material/transition/platform/FitModeResult.class", "name": "com/google/android/material/transition/platform/FitModeResult.class", "size": 803, "crc": 1290980785}, {"key": "com/google/android/material/transition/platform/Hold.class", "name": "com/google/android/material/transition/platform/Hold.class", "size": 1252, "crc": -219407828}, {"key": "com/google/android/material/transition/platform/MaskEvaluator.class", "name": "com/google/android/material/transition/platform/MaskEvaluator.class", "size": 3734, "crc": 1452754079}, {"key": "com/google/android/material/transition/platform/MaterialArcMotion.class", "name": "com/google/android/material/transition/platform/MaterialArcMotion.class", "size": 1220, "crc": 1975324863}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$1.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$1.class", "size": 1805, "crc": -1380916687}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$2.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$2.class", "size": 2610, "crc": 2041369205}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$FadeMode.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$FadeMode.class", "size": 729, "crc": -1729263935}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$FitMode.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$FitMode.class", "size": 727, "crc": 15814459}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$ProgressThresholds.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$ProgressThresholds.class", "size": 1338, "crc": 590958262}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$ProgressThresholdsGroup.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$ProgressThresholdsGroup.class", "size": 2850, "crc": -25750488}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDirection.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDirection.class", "size": 751, "crc": -1258979985}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable$1.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable$1.class", "size": 1488, "crc": -853716702}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable$2.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable$2.class", "size": 1486, "crc": 1842957974}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform$TransitionDrawable.class", "size": 18113, "crc": 1765276026}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransform.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransform.class", "size": 22463, "crc": 285540632}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$1.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$1.class", "size": 1421, "crc": -1266201878}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$2.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$2.class", "size": 1797, "crc": 226787342}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$3.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$3.class", "size": 1352, "crc": -**********}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$ShapeProvider.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$ShapeProvider.class", "size": 647, "crc": 681150451}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$ShapeableViewShapeProvider.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback$ShapeableViewShapeProvider.class", "size": 1444, "crc": -212006488}, {"key": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback.class", "name": "com/google/android/material/transition/platform/MaterialContainerTransformSharedElementCallback.class", "size": 10096, "crc": 15886912}, {"key": "com/google/android/material/transition/platform/MaterialElevationScale.class", "name": "com/google/android/material/transition/platform/MaterialElevationScale.class", "size": 3120, "crc": 313353888}, {"key": "com/google/android/material/transition/platform/MaterialFade.class", "name": "com/google/android/material/transition/platform/MaterialFade.class", "size": 4450, "crc": -81722396}, {"key": "com/google/android/material/transition/platform/MaterialFadeThrough.class", "name": "com/google/android/material/transition/platform/MaterialFadeThrough.class", "size": 3642, "crc": -**********}, {"key": "com/google/android/material/transition/platform/MaterialSharedAxis$Axis.class", "name": "com/google/android/material/transition/platform/MaterialSharedAxis$Axis.class", "size": 697, "crc": -**********}, {"key": "com/google/android/material/transition/platform/MaterialSharedAxis.class", "name": "com/google/android/material/transition/platform/MaterialSharedAxis.class", "size": 4471, "crc": **********}, {"key": "com/google/android/material/transition/platform/MaterialVisibility.class", "name": "com/google/android/material/transition/platform/MaterialVisibility.class", "size": 6356, "crc": **********}, {"key": "com/google/android/material/transition/platform/ScaleProvider$1.class", "name": "com/google/android/material/transition/platform/ScaleProvider$1.class", "size": 1036, "crc": -**********}, {"key": "com/google/android/material/transition/platform/ScaleProvider.class", "name": "com/google/android/material/transition/platform/ScaleProvider.class", "size": 3822, "crc": -258198132}, {"key": "com/google/android/material/transition/platform/SlideDistanceProvider$1.class", "name": "com/google/android/material/transition/platform/SlideDistanceProvider$1.class", "size": 1005, "crc": -457269628}, {"key": "com/google/android/material/transition/platform/SlideDistanceProvider$2.class", "name": "com/google/android/material/transition/platform/SlideDistanceProvider$2.class", "size": 1005, "crc": **********}, {"key": "com/google/android/material/transition/platform/SlideDistanceProvider$GravityFlag.class", "name": "com/google/android/material/transition/platform/SlideDistanceProvider$GravityFlag.class", "size": 720, "crc": 304001187}, {"key": "com/google/android/material/transition/platform/SlideDistanceProvider.class", "name": "com/google/android/material/transition/platform/SlideDistanceProvider.class", "size": 5588, "crc": 414867973}, {"key": "com/google/android/material/transition/platform/TransitionListenerAdapter.class", "name": "com/google/android/material/transition/platform/TransitionListenerAdapter.class", "size": 1154, "crc": -**********}, {"key": "com/google/android/material/transition/platform/TransitionUtils$1.class", "name": "com/google/android/material/transition/platform/TransitionUtils$1.class", "size": 1961, "crc": **********}, {"key": "com/google/android/material/transition/platform/TransitionUtils$CornerSizeBinaryOperator.class", "name": "com/google/android/material/transition/platform/TransitionUtils$CornerSizeBinaryOperator.class", "size": 602, "crc": 234530973}, {"key": "com/google/android/material/transition/platform/TransitionUtils.class", "name": "com/google/android/material/transition/platform/TransitionUtils.class", "size": 14017, "crc": -**********}, {"key": "com/google/android/material/transition/platform/VisibilityAnimatorProvider.class", "name": "com/google/android/material/transition/platform/VisibilityAnimatorProvider.class", "size": 568, "crc": 586596212}, {"key": "META-INF/com.google.android.material_material.version", "name": "META-INF/com.google.android.material_material.version", "size": 7, "crc": -582916192}]