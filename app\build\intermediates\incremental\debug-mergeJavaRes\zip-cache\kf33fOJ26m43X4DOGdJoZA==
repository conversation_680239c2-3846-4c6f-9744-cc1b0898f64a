[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 46, "crc": 1570773839}, {"key": "kotlinx/coroutines/android/HandlerContext.class", "name": "kotlinx/coroutines/android/HandlerContext.class", "size": 8097, "crc": 1371274364}, {"key": "kotlinx/coroutines/android/HandlerContext$scheduleResumeAfterDelay$1.class", "name": "kotlinx/coroutines/android/HandlerContext$scheduleResumeAfterDelay$1.class", "size": 1831, "crc": -1286505402}, {"key": "kotlinx/coroutines/android/HandlerDispatcherKt.class", "name": "kotlinx/coroutines/android/HandlerDispatcherKt.class", "size": 10323, "crc": 18663672}, {"key": "kotlinx/coroutines/android/AndroidExceptionPreHandler.class", "name": "kotlinx/coroutines/android/AndroidExceptionPreHandler.class", "size": 3076, "crc": -84991028}, {"key": "kotlinx/coroutines/android/HandlerDispatcherKt$awaitFrameSlowPath$lambda$3$$inlined$Runnable$1.class", "name": "kotlinx/coroutines/android/HandlerDispatcherKt$awaitFrameSlowPath$lambda$3$$inlined$Runnable$1.class", "size": 1860, "crc": 1513759811}, {"key": "kotlinx/coroutines/android/AndroidDispatcherFactory.class", "name": "kotlinx/coroutines/android/AndroidDispatcherFactory.class", "size": 2203, "crc": -82887242}, {"key": "kotlinx/coroutines/android/HandlerDispatcher.class", "name": "kotlinx/coroutines/android/HandlerDispatcher.class", "size": 2404, "crc": 49344002}, {"key": "kotlinx/coroutines/android/HandlerContext$scheduleResumeAfterDelay$$inlined$Runnable$1.class", "name": "kotlinx/coroutines/android/HandlerContext$scheduleResumeAfterDelay$$inlined$Runnable$1.class", "size": 2428, "crc": -315594454}, {"key": "META-INF/kotlinx-coroutines-android.kotlin_module", "name": "META-INF/kotlinx-coroutines-android.kotlin_module", "size": 75, "crc": -1459020172}, {"key": "META-INF/com.android.tools/r8-from-1.6.0/coroutines.pro", "name": "META-INF/com.android.tools/r8-from-1.6.0/coroutines.pro", "size": 899, "crc": 2032253094}, {"key": "META-INF/com.android.tools/proguard/coroutines.pro", "name": "META-INF/com.android.tools/proguard/coroutines.pro", "size": 300, "crc": -477435061}, {"key": "META-INF/com.android.tools/r8-upto-3.0.0/coroutines.pro", "name": "META-INF/com.android.tools/r8-upto-3.0.0/coroutines.pro", "size": 558, "crc": 1737114335}, {"key": "META-INF/services/kotlinx.coroutines.CoroutineExceptionHandler", "name": "META-INF/services/kotlinx.coroutines.CoroutineExceptionHandler", "size": 54, "crc": -1889973424}, {"key": "META-INF/services/kotlinx.coroutines.internal.MainDispatcherFactory", "name": "META-INF/services/kotlinx.coroutines.internal.MainDispatcherFactory", "size": 52, "crc": 1268078975}, {"key": "META-INF/proguard/coroutines.pro", "name": "META-INF/proguard/coroutines.pro", "size": 419, "crc": 77044690}, {"key": "META-INF/versions/9/module-info.class", "name": "META-INF/versions/9/module-info.class", "size": 381, "crc": -706149332}, {"key": "META-INF/kotlinx_coroutines_android.version", "name": "META-INF/kotlinx_coroutines_android.version", "size": 5, "crc": 1781465880}]