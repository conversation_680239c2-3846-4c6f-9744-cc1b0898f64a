{"logs": [{"outputFile": "com.example.th2_1.app-mergeDebugResources-33:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c44c5e9b00e7cb770ebac8dfe17166cf\\transformed\\material-1.13.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,589,697,809,892,948,1012,1104,1173,1232,1321,1406,1469,1531,1589,1653,1714,1779,1845,1900,1954,2068,2126,2186,2240,2310,2437,2518,2608,2707,2804,2883,3018,3094,3171,3300,3384,3465,3520,3575,3641,3710,3787,3858,3937,4005,4081,4151,4216,4318,4413,4486,4580,4673,4747,4816,4910,4966,5049,5116,5200,5288,5350,5414,5477,5544,5641,5734,5825,5916,6015,6112,6171,6230,6307,6392,6468", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "endColumns": "12,76,78,80,88,107,111,82,55,63,91,68,58,88,84,62,61,57,63,60,64,65,54,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,80,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,92,90,90,98,96,58,58,76,84,75,72", "endOffsets": "258,335,414,495,584,692,804,887,943,1007,1099,1168,1227,1316,1401,1464,1526,1584,1648,1709,1774,1840,1895,1949,2063,2121,2181,2235,2305,2432,2513,2603,2702,2799,2878,3013,3089,3166,3295,3379,3460,3515,3570,3636,3705,3782,3853,3932,4000,4076,4146,4211,4313,4408,4481,4575,4668,4742,4811,4905,4961,5044,5111,5195,5283,5345,5409,5472,5539,5636,5729,5820,5911,6010,6107,6166,6225,6302,6387,6463,6536"}, "to": {"startLines": "2,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2975,3052,3131,3212,4031,4139,4251,4334,4390,4454,4546,4615,4674,4763,4848,4911,4973,5031,5095,5156,5221,5287,5342,5396,5510,5568,5628,5682,5752,5879,5960,6050,6149,6246,6325,6460,6536,6613,6742,6826,6907,6962,7017,7083,7152,7229,7300,7379,7447,7523,7593,7658,7760,7855,7928,8022,8115,8189,8258,8352,8408,8491,8558,8642,8730,8792,8856,8919,8986,9083,9176,9267,9358,9457,9554,9613,9672,9829,9914,9990", "endLines": "5,33,34,35,36,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,76,78,80,88,107,111,82,55,63,91,68,58,88,84,62,61,57,63,60,64,65,54,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,80,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,92,90,90,98,96,58,58,76,84,75,72", "endOffsets": "308,3047,3126,3207,3296,4134,4246,4329,4385,4449,4541,4610,4669,4758,4843,4906,4968,5026,5090,5151,5216,5282,5337,5391,5505,5563,5623,5677,5747,5874,5955,6045,6144,6241,6320,6455,6531,6608,6737,6821,6902,6957,7012,7078,7147,7224,7295,7374,7442,7518,7588,7653,7755,7850,7923,8017,8110,8184,8253,8347,8403,8486,8553,8637,8725,8787,8851,8914,8981,9078,9171,9262,9353,9452,9549,9608,9667,9744,9909,9985,10058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5f6f8bf2c7a7ef52f138f50dee19f9\\transformed\\core-1.17.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,558,656,785", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "148,250,350,451,553,651,780,881"}, "to": {"startLines": "37,38,39,40,41,42,43,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3301,3399,3501,3601,3702,3804,3902,10063", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "3394,3496,3596,3697,3799,3897,4026,10159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74e2cf8db6b03b348cce7b0ce514ee13\\transformed\\appcompat-1.7.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,9749", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,9824"}}]}]}